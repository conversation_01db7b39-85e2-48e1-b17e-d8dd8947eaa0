const util = require('util');
const inspect = util.inspect;

const helpers = require(`../../../../functions/helpers`);
const CONSTANTS = require(`../../../../functions/constants`);
const ROLES = require(`../../../../functions/constants/roles`)
const COLLECTIONS = require(`../../../../functions/constants/collections`)

const { isLoopable, momentNow } = helpers;

const getStoredFileUrl = (storageBucket, filePath) => {

	const file = storageBucket.file(filePath);

	return file.exists()
	.then( data => {
		const exists = Boolean(data[0]);
		if( exists ) 
			return file.getSignedUrl({ action: 'read', expires: '2100-01-01T00:00:00'})
		else return ['']
	})
	.then(urls => {
		const url = urls[0];
		return url
	})
	.catch(error => {
		console.log(`Unable to upload file ${error}`)
		return {error}
	})
}

const uploadBase64File = async (storageBucket, base64Str, filePath, metadata, checkPath) => {
	
	if ( checkPath===true ) {
		let fileUrl = await getStoredFileUrl(filePath);
		if (fileUrl) return new Promise(r=>r(fileUrl));
	}

	const buffer = Buffer.from(base64Str, 'base64')
	const bytesArray = new Uint8Array(buffer);
	const file = storageBucket.file(filePath);
	const options = { resumable: false, metadata: { ...metadata||{filePath} } };

	//options may not be necessary
	return file.save(bytesArray, options)
	.then(stuff => {
		return file.getSignedUrl({ action: 'read', expires: '2100-01-01T00:00:00'})
	})
	.then(urls => {
		const url = urls[0];
		return url
	})
	.catch(error => {
		console.log(`Unable to upload file ${error}`)
		return {error}
	})
	
}

const testImapClient = () => {

	var ImapClient = require('emailjs-imap-client').default;

	var client = new ImapClient(
		'imappro.zoho.com',
		993,
		{ 
			// useSecureTransport: true,
			requireTLS: true,
			// ignoreTLS: true,
			auth: {
				user: '<EMAIL>',
				pass: 'Fck123@Amg!',
			} 
		})


	client
	.selectMailbox('INBOX')
	.then((mailboxe) => { 
		console.log('mailboxe',mailboxe);		
	})
	.catch(err=>{
		console.error('selectMailbox > err', err);
	})

	// client.listMessages('INBOX', '1:10', ['uid', 'flags', 'body[]']).then((messages) => {
	// 	messages.forEach((message) => console.log('Flags for ' + message.uid + ': ' + message.flags.join(', ')));
	// });

	client.onerror = function(error){
		console.error({ error });
	}

}

const testImapSimple = () => {

	var imaps = require('imap-simple');

	var config = {
		imap: {
			user: '<EMAIL>',
			password: 'Fck123@Amg!',
			host: 'imappro.zoho.com',
			port: 993,
			tls: true,
			authTimeout: 10000
		}
	};

	imaps.connect(config).then(function (connection) {

    return connection.openBox('INBOX').then(function () {
        var searchCriteria = [
            'UNSEEN'
        ];

        var fetchOptions = {
            bodies: ['HEADER', 'TEXT'],
            markSeen: false
        };

        return connection.search(searchCriteria, fetchOptions).then(function (results) {
            var subjects = results.map(function (res) {
                return res.parts.filter(function (part) {
                    return part.which === 'HEADER';
                })[0].body.subject[0];
            });

            console.log(subjects);
            // =>
            //   [ 'Hey Chad, long time no see!',
            //     'Your amazon.com monthly statement',
            //     'Hacker Newsletter Issue #445' ]
        });
    });
});

}

function findTextPart(struct) {
	for (var i = 0, len = struct.length, r; i < len; ++i) {
		if (Array.isArray(struct[i])) {
			r = findTextPart(struct[i])
			if (r) return r;
		} else 
		if ( struct[i].type === 'text' && 
			(struct[i].subtype === 'plain' || struct[i].subtype === 'html')
		)
		return [struct[i].partID, struct[i].type + '/' + struct[i].subtype];
	}
}

const getImapAttachments = (data, uploadAttachments) => {

	return new Promise(res=>{

		let uploads = [];
		let attachments = [];
		let { raw, seqno, folder, user } = data, matches = /--[^\n\r]*\r?\nContent-Type:[\s\S]*?\r?\n\r?\n/g.exec(raw);
	
		while (matches) {

			let info = matches[0];

			try {

				let part = (info.match(/--[^\n\r]*\r?/g)||[])[0]
				let contentType = (/Content-Type:(.*)/.exec(info)||[''])[0].split(';')[0].replace('Content-Type: ','');
				let contentId = (/Content-ID: (.*)/.exec(info)||[''])[0].replace(/Content-ID: |<|>/g,'')||'';
				let filename = (/filename=(.*)/.exec(info)||[''])[0].replace(/filename=|"/g,'');
				let isBase64 = /Content-Transfer-Encoding: base64(.*)/.exec(info);
				let isApplication = contentType.match(/application\//);

				let content = raw.split(info)[1];
				let nextInfo = (/--[^\n\r]*\r?\nContent-Type[\s\S]*?\r?\n\r?\n/g.exec(content)||[])[0]

				if ( part && (content.match(part)||[])[0] ) {
					content = content.split(part)[0];
				}else
				if ( nextInfo ) {						
					content = content.split(nextInfo)[0];
				}else
				if ( content.split('--')[0] ) {
					content = content.split('--')[0];
				}else{
					content = content.split('=')[0]+'=';
				}

				content = content.split('--')[0];

				console.log(`#${seqno} getImapAttachments > info`,{ info, contentType, isBase64 });

				if ( isApplication ) {

					const userPath = user.replace('@','-at-');
					const folderPath = folder.toLowerCase().replace(/ /g,'');
					const filePath = `attachments/${userPath}/${folderPath}/${seqno}/${filename}`;
					const metadata = { contentType, customMetadata: { seqno, folder, filePath, user } };

					let attachment = {
						attachment_url: '',
						preview_url: '',
						attachment_id: contentId,
						name: filename,
						mimeType: contentType,
						contentBytes: '',
						metadata
					}

					uploads.push({ content, filePath, metadata, attachment })
					
					raw = raw.split(content)[1];

				}else
				if ( isBase64 ) {
					
					let attachment = {
						attachment_url: '',
						preview_url: '',
						attachment_id: contentId,
						name: filename,
						mimeType: contentType,
						contentBytes: content
					}
		
					attachments.push(attachment)
					
					raw = raw.split(content)[1];

				}else{
					raw = raw.replace(info,'');
				}
				
				matches = /--[^\n\r]*\r?\nContent-Type:[\s\S]*?\r?\n\r?\n/g.exec(raw)
				
			} catch (error) {
				
				console.log(`#${seqno} getImapAttachments > error`,error);			
				raw = raw.replace(info,'');
				matches = /--[^\n\r]*\r?\nContent-Type:[\s\S]*?\r?\n\r?\n/g.exec(raw)

			}
		
		}

		const promises = uploads.map(({ content, filePath, metadata, attachment })=>
			new Promise(async res=>{
				let fileUrl = await getStoredFileUrl(filePath);
				console.log(`#${seqno} fileUrl`,fileUrl);				
				if (fileUrl && typeof fileUrl === 'string') {
					return res({ attachment: {
						...attachment,
						attachment_url: fileUrl,
					}})
				}else
				if ( uploadAttachments===true ) {
					return uploadBase64File(content, filePath, metadata, true)
					.then(url=>{
						console.log(`#${seqno} uploadBase64File > url`,url);
						if (typeof url === 'string') {
							return res({ attachment: {
								...attachment,
								attachment_url: url,
							}})
						}
						return res({ error: url })						
					})
					.catch(error=>{
						console.log(`#${seqno} uploadBase64File > error`,error);		
						return res({ error })
					})
				}else
				return res({ attachment })
			})
		)
		
		return Promise.all(promises)
		.then(results=>{
			results.forEach(({ attachment, error })=>{
				if (attachment) attachments.push(attachment)
			})
			console.log(`#${seqno} getImapAttachments > attachments`,{ attachments });
			return res({ attachments })
		})
		.catch(error=>{
			console.log(`#${seqno} uploadBase64File > error`,error);		
			return res({ error, attachments: [] })
		})
	})

}

const getIMAPFolders = data => {
	
	const results = {};
	
	let { config } = data;

	const Imap = require('imap');
	let imap = new Imap(config);
	
	results.errors = []
	
	function imapNestedFolders(folders) {
		var FOLDERS = [];
		var folder  = {};		

		try {
			console.log('getIMAPFolders > folders', inspect(folders, true, 10, true));			
		} catch (error) {
			console.log('getIMAPFolders > error', { error });
		}
		
		if (isLoopable(folders)) 
			for (var key in folders) {
				let f = folders[key];
				// console.log('getIMAPFolders > folder',f);
				if ((f.attribs||'').indexOf('\\HasChildren') > -1) {		
					var children = imapNestedFolders(f.children);
					folder = {
						key,
						name: key,
						children,
						data: { ...f, key, parent: null, children: null }
					};		
				} else {
					folder = {
						key,
						name: key,
						children: null,
						data: { ...f, key, parent: null, children: null }
					};
				}
				FOLDERS.push(folder);		
			}

		return FOLDERS;
	}

	imap.once('ready', () => {
		
		imap.getBoxes((err, boxes) => {

			if (err) console.log('ERROR_GETTING_MAILBOXES',err);
			if (err) return console.log({ error: 'ERROR_GETTING_MAILBOXES', err, results });
		
			try {
			
				let folders = imapNestedFolders(boxes);
				results.folders = folders;				
				
				// console.log('getBoxes > folders',inspect(folders, true, 10, true));
				return console.log(inspect({ results },true,10,true));

			} catch (error) {
				console.log('Error fetching folders > results', { error });
				return console.log({ error: 'ERROR_RETRIEVING_MAILBOXES', err: error, results });
			}

		});
			
	});
	
	imap.once('error', (err) => {
		console.log('ERROR_RETRIEVING_MAILBOXES',err);
		return console.log({ error: 'ERROR_RETRIEVING_MAILBOXES', err, results });
	});
	
	imap.once('end', () => {
		console.log('Connection ended');
		return console.log({ results });
	});
	
	imap.connect();
}

const getIMAPMail = data => {

	const results = {};

	if (!data || !data.config) {
		// console.log('onRequest > request', request);
		// throw new functions.https.HttpsError('failed-precondition', 'The function needs arguments.' + JSON.stringify(request.body));
		return null;
	}

	if (!data.config.user || !data.config.password || !data.config.host) {
		return console.log({ data: { ...data, error: 'MISSING_REQUIRED_FIELDS' } });
	}

	console.log('getIMAPMail > data',{ data });

	let { config, config: { user }, folder, range, action } = data;

	let page = data.page||'';
	let perpage = data.perpage||'';

	const simpleParser = require('mailparser').simpleParser;
	const quotedPrintable = require('quoted-printable');
	const utf8 = require('utf8');
	const Imap = require('imap');
	let imap = new Imap(config);
	
	results.errors = []
	
	imap.once('ready', () => {
		
		imap.openBox(folder, true, (err, box) => {
			
			if (err) return console.log({ data: { error: 'ERROR_OPENING_MAILBOX', err, results } });
			
			const { total } = box.messages;

			results.total = total;
			results.messages = {}

			let lastMsg = `${total}:*`;
			let lastRange = perpage ? `${total}:${total-perpage}` : lastMsg;
			if ( page && perpage ) {
				let i = perpage*(page-1);
				lastRange = `${total-i}:${total-perpage-i}`;
			}
			
			let paged = range==='last'?lastRange:range;
			
			console.log('getIMAPMail > paged',{ range, paged });
			
			let f;
			switch (action) {
				case 'read':
					f = imap.seq.fetch(paged, { bodies: ['HEADER.FIELDS (FROM TO CC BCC SUBJECT DATE)','TEXT'] });
				break;			
				case 'list':
				default:
					// f = imap.seq.fetch(paged, { bodies: 'HEADER.FIELDS (FROM TO CC BCC SUBJECT DATE)', struct: true });
					f = imap.seq.fetch(paged, { bodies: ['HEADER.FIELDS (FROM TO CC BCC SUBJECT DATE)','TEXT'] });
				break;
			}

			f.on('message', async (msg, seqno) => {

				console.log('Message #%d', seqno);
				var prefix = '(#' + seqno + ') ';
				
				results.messages[seqno] = {
					info: {},
					buffers: [],
					parsers: []
				};
				
				var raw = '';
				msg.on('body', async (stream, info) => {
					
					const { which, size } = info;
					
					let isBody = which === 'TEXT';

					results.messages[seqno].info[which.split(' ')[0]] = info;

					if ( isBody ) {
						console.log(prefix + 'Body [%s] found, %d total bytes', inspect(info.which), info.size);
						results.messages[seqno].size = size;
					}

				  	var buffer = '', count = 0;
				  	stream.on('data', (chunk) => {
						count += chunk.length;
						buffer += chunk.toString('utf8');
						raw += chunk;
						if ( !isBody ) {
							console.log(prefix + 'Header count/size: (%d/%d)', count, info.size);
						}else{
							console.log(prefix + 'Body count/size: (%d/%d)', count, info.size);
						}
					});

				  	stream.once('end', async () => {
						if ( !isBody ) {
							console.log(prefix + 'Parsed header: %s', inspect(Imap.parseHeader(buffer)));
							results.messages[seqno].header = Imap.parseHeader(buffer);
							results.messages[seqno].rawHeader = buffer;
						} else {
							console.log(prefix + 'Body [%s] Finished', inspect(info.which));
							let body = buffer;
							if ( size < 1000000 ) {
								results.messages[seqno].body = body;
							}
						}
						// results.messages[seqno].buffers.push(buffer);
					});

				});

				msg.once('attributes', (attrs) => {
					// console.log(prefix + 'Attributes: %s', attrs);
					results.messages[seqno].uid = attrs.uid||'';
					results.messages[seqno].attributes = attrs;
				});

				msg.once('end', async () => {
					console.log(prefix + 'Finished');
					results.messages[seqno].raw = raw;
					results.messages[seqno].end = true;
				});

			});
			
			f.once('error', (err) => {
				console.log('Fetch error: ' + err);
				results.errors.push(err)
				results.error = true;
				return console.log({ data: { error: 'ERROR_RETRIEVING_MAILBOX', err, results } });
			});

			f.once('end', async () => {
				
				let promises = isLoopable(results.messages) ? 
					Object.keys(results.messages).map((seqno,i)=>{
						return new Promise(async res=>{
							console.log(`simpleParser > rawEmail > #${seqno}`);
							// let rawEmail = results.messages[seqno].buffers.reduce((a,b)=>a+b,'');
							const { raw, size } = results.messages[seqno];
							let parsed = await simpleParser(raw);
							
							const shouldUpload = action==='read';
							const { attachments, error } = await getImapAttachments({ raw, seqno, folder, user }, shouldUpload);
							
							if ( size > 1000000 ) results.messages[seqno].raw = '';

							return res({ seqno, parsed, attachments })
						})
					})
					: []
				
				return Promise.all(promises)
				.then(r=>{
					
					r.forEach(p=>{

						let { seqno, parsed, attachments } = p, { text } = parsed, html = text;

						let htmlContent;
						try {
							htmlContent = utf8.decode(quotedPrintable.decode(html));
						} catch (error) {
							htmlContent = quotedPrintable.decode(html);
						}
					
						[/--[^\n\r]*\r?\nContent-Type: text\/html[\s\S]*?\r?\n\r?\n/g, /--[^\n\r]*\r?\nContent-Type: text\/plain[\s\S]*?\r?\n\r?\n/g]
						.forEach(regEx=>{
							let info = regEx.exec(htmlContent)        
							console.log(`regEx > info #${seqno}`,info);
							if ( info ) {
								let part = (info[0].match(/--[^\n\r]*\r?/g)||[])[0]
								let content = htmlContent.split(info)[1];
								if ( part && (content.match(part)||[])[0] ) {
									htmlContent = content.split(part)[0];    
								}else{
									let nextInfo = /\n--[^\n\r]*\r?\nContent-Type[\s\S]*?\r?\n\r?\n/g.exec(content)
									htmlContent = content.split(nextInfo[0])[0];
								}
								html = htmlContent;
							}
						})
												
						// results.messages[seqno].parsed = parsed;
						results.messages[seqno].html = html;
						results.messages[seqno].attachments = attachments;
						// results.messages[seqno].html = parsed.textAsHtml;

						// console.log(`simpleParser > rawEmail > #${seqno}`,inspect(parsed, true, 10, true));
					})
					
					results.end = true;
					imap.end();
					
					console.log('Done fetching all messages');
					// console.log('Done fetching all messages > results', results);
					return console.log({ data: { results }});
					
				})
				
			});

			return null;
		});

	});
	
	imap.once('error', (err) => {
		console.log(err);
		return console.log({ data: { error: err.type==='no' && err.source==='authentication' ? 'AUTH_ERROR' : 'ERROR_RETRIEVING_EMAIL', err, results } });
	});
	
	imap.once('end', () => {
		console.log('Connection ended');
		return console.log({ data: { results }});
	});
	
	return imap.connect();

}

const btoa=b=>Buffer.from(b).toString('base64');	
const atob=s=>Buffer.from(s,'base64').toString('binary');

const b64DecodeUnicode = (str) => {
	// Going backwards: from bytestream, to percent-encoding, to original string.
	return decodeURIComponent(atob(str.replace(/-/g, '+').replace(/_/g, '/')).split('').map(function(c) {
		return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
	}).join(''));
}

module.exports = {
    getStoredFileUrl,
    uploadBase64File,
    testImapClient,
    testImapSimple,
    getImapAttachments,
    b64DecodeUnicode,
    btoa,
    atob
}