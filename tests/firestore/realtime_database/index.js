const firebase = require('firebase');
const moment = require('moment');

const helpers = require(`../../../../functions/helpers`);
const CONSTANTS = require(`../../../../functions/constants`);
const ROLES = require(`../../../../functions/constants/roles`)
const COLLECTIONS = require(`../../../../functions/constants/collections`)

const database = firebase.database();
const DatabaseRef = database.ref();

const cloneToFirestore = (collection, startKey, workRef) => {
	
	let errIds = [];
	let errCounter = 0;
	let successCounter = 0;
	let counter = 0;
	let limit = 30000;

	var childrenRef = DatabaseRef.child(collection)

	startKey && (childrenRef = childrenRef.orderByKey().startAt(startKey).limitToFirst(limit))
	
	childrenRef.once('value')
	.then(function (snapshot) { 
		
		let numChildren = snapshot.numChildren()
		let children = snapshot.val()
		let lastKey = children && Object.keys(children).pop()
		
		console.log(`Cloning ${numChildren} ${collection} from Realtime Database to Firestore`);

		snapshot.forEach(function (childSnapshot) { 

			let child = childSnapshot.val();
			let childKey = childSnapshot.key;

			if (collection === 'stores' && child.leads) {
				delete child.leads
			}

			try {
	
				workRef.collection(collection)
					.doc(childKey.toString())
					.set(child)
					.then(function () {
						// console.log("Success ID: ", childKey);
						successCounter++;
						counter++;
						counter === numChildren && console.log('', {
							collection,
							successCounter,
							errCounter,
							counter,
							id: childKey,
							time: moment().format(CONSTANTS.MOMENT_ISO),
							lastKey
						});
					})
					.catch(function (error) {
						errIds.push(childKey)
						errCounter++;
						counter++;
						counter === numChildren && console.log('', {
							collection,
							successCounter,
							errCounter,
							counter,
							id: childKey,
							time: moment().format(CONSTANTS.MOMENT_ISO),
							lastKey
						});
						console.log(" ");
						console.log("Error errCounter:", errCounter);
						console.log("Error errIds:", errIds.toString());
						console.log("Error adding document ID:", childKey);
						console.error("Error adding document ERROR: ", ((error.toString && error.toString())||error));
						// console.log("child:", child);
					});

			} catch (error) { 
				
				errIds.push(childKey)
				errCounter++;
				counter++;
				counter === numChildren && console.log('', {
					collection,
					successCounter,
					errCounter,
					counter,
					id: childKey,
					time: moment().format(CONSTANTS.MOMENT_ISO),
					lastKey
				});
				console.log(" ");
				console.log("Catch errCounter:", errCounter);
				console.log("Catch errIds:", errIds.toString());
				console.log("Catch adding document ID:", childKey);
				console.error("Catch adding document ERROR: ", ((error.toString && error.toString()) || error));
				console.log("child:", child);
				// console.log("child:", JSON.stringify(child));

			}

		})

	}).catch(err=>console.log('DatabaseRef > error en promesa:',err));
}

const cloneToRealtimeDatabase = (collection, startKey, workRef) => {
	
	let errIds = [];
	let errCounter = 0;
	let successCounter = 0;
	let counter = 0;
	let limit = 30000;

	var childrenRef = workRef.collection(collection)
	// startKey && (childrenRef = childrenRef.startAt(startKey).limit(limit))
	
	// var childrenRef = DatabaseRef.child(collection)
	// startKey && (childrenRef = childrenRef.orderByKey().startAt(startKey).limitToFirst(limit))
	
	childrenRef.get()
	.then(function (snapshot) { 
		
		let numChildren = snapshot.size
		let children = snapshot.docs
		let lastKey = children && children.pop().data().ID
		
		console.log(`Cloning ${numChildren} ${collection} from Firestore to Realtime Database`);

		snapshot.forEach(function (doc) { 

			let child = doc.data();
			let childKey = doc.id;

			try {
	
				DatabaseRef.child(`${collection}/${childKey.toString()}`)
					.set(child)
					.then(function () {
						// console.log("Success ID: ", childKey);
						successCounter++;
						counter++;
						counter === numChildren && console.log('', {
							collection,
							successCounter,
							errCounter,
							counter,
							id: childKey,
							time: moment().format(CONSTANTS.MOMENT_ISO),
							lastKey
						});
					})
					.catch(function (error) {
						errIds.push(childKey)
						errCounter++;
						counter++;
						counter === numChildren && console.log('', {
							collection,
							successCounter,
							errCounter,
							counter,
							id: childKey,
							time: moment().format(CONSTANTS.MOMENT_ISO),
							lastKey
						});
						console.log(" ");
						console.log("Error errCounter:", errCounter);
						console.log("Error errIds:", errIds.toString());
						console.log("Error adding document ID:", childKey);
						console.error("Error adding document ERROR: ", ((error.toString && error.toString())||error));
						// console.log("child:", child);
					});

			} catch (error) { 
				
				errIds.push(childKey)
				errCounter++;
				counter++;
				counter === numChildren && console.log('', {
					collection,
					successCounter,
					errCounter,
					counter,
					id: childKey,
					time: moment().format(CONSTANTS.MOMENT_ISO),
					lastKey
				});
				console.log(" ");
				console.log("Catch errCounter:", errCounter);
				console.log("Catch errIds:", errIds.toString());
				console.log("Catch adding document ID:", childKey);
				console.error("Catch adding document ERROR: ", ((error.toString && error.toString()) || error));
				console.log("child:", child);
				// console.log("child:", JSON.stringify(child));

			}

		})

	}).catch(err=>console.log('DatabaseRef > error en promesa:',err));
}

const migrateToFirestore = (collection, startKey, workRef) => {
	
	let errIds = [];
	let errCounter = 0;
	let successCounter = 0;
	let counter = 0;
	let limit = 30000;

	var childrenRef = DatabaseRef.child(collection)

	startKey && (childrenRef = childrenRef.orderByKey().startAt(startKey).limitToFirst(limit))
	
	childrenRef.once('value')
	.then(function (snapshot) { 
		
		let numChildren = snapshot.numChildren()
		let children = snapshot.val()
		let lastKey = children && Object.keys(children).pop()
		
		console.log(`Migrating ${numChildren} ${collection} from Realtime Database to Firestore`);

		snapshot.forEach(function (childSnapshot) { 

			let child = childSnapshot.val();
			let childKey = childSnapshot.key;

			Array.isArray(child.leads) && (delete child.leads)

			child.id = childKey;
			child.ID = childKey;

			if ((!child.stores || !Array.isArray(child.stores)) || !child.stores.length ) {
				if ( child.lojas ) {
					child.stores = child.lojas;
					delete child.lojas;
				}else{
					errIds.push(child.ID)
					errCounter++;
					counter++;
					console.log('ERRR NO stores:', {
						collection,
						successCounter,
						errCounter,
						counter,
						id: childKey,
						lastKey
					});
					counter === numChildren && console.log('', {
						collection,
						successCounter,
						errCounter,
						counter,
						id: childKey,
						time: moment().format(CONSTANTS.MOMENT_ISO),
						lastKey
					});
					return false;
				}
			}else{
				child.stores.forEach((storeId,i) => {
					if (typeof storeId==='number') {
						child.stores[i] = storeId.toString();
					}
				});
			}
			
			// if ( child.stores && child.stores.length ) {	
			// }
			
			// ---------------------------------------------------------------------
			// SPECIFIC FR COLLECTIONS
			// ---------------------------------------------------------------------
			
			if (collection === 'events'||collection === 'campaigns') {
				
				if ( child.users_check_in ) {
	
					Object.keys(child.users_check_in).forEach((uId,i)=>{
						const isChecked = child.users_check_in[uId];
						if (!isChecked) {
							delete child.users_check_in[uId]
						}
					})
	
					child.users_check_in = Object.keys(child.users_check_in)
	
				}
				
				if ( child.participants ) {
					delete child.participants
				}

				if ( child.participantes ) {
					// child.participants = child.participantes;
					delete child.participantes
				}

			}

			if (collection === 'funnels') {

				if (child.products && !Array.isArray(child.products) && typeof child.products === 'object' && Object.keys(child.products).length) {
					child.products = Object.keys(child.products)
				}

				const funnelDeleteKeys = ['values', 'users']

				funnelDeleteKeys.forEach((key, i) => {
					if (key in child) (delete child[key])
				})

				const deepDeleteKeys = ['users', 'calc', 'btn', 'quick_add_tasklist', 'meta_diaria', 'meta_final', 'meta_mensal', 'meta_semanal', 'metas_vendedor', 'metas_conversao', 'metas_leads', 'metas_leads_vendedor']
				
				helpers.deleteRecursively(child, deepDeleteKeys);
				
				if (child.pipeline) {
					delete child.pipeline;
				}

				if (child.pipeline_config) {
					child.pipeline = child.pipeline_config;
					delete child.pipeline_config;
				}	

			}

			// ---------------------------------------------------------------------
			// GENERAL
			// ---------------------------------------------------------------------
			const replaceKeys = {
				cor: 'color',
				ativo: 'active',
				ativa: 'active',
				participantes: 'participants',
				administradores: 'admins',
				promotores: 'promoters',
			}

			Object.keys(replaceKeys).forEach((key, i) => {
				if(key in child) (child[replaceKeys[key]] = child[key])
				if(key in child) (delete child[key])
			})

			const deleteKeys = ['confirmacao', 'tipo', 'lojas', 'check_in', 'offlineCheckIn', 'offlineRegister', 'assoc_ids', 'assoc_types', 'entrance_types']

			deleteKeys.forEach((key, i) => {
				if(key in child) (delete child[key])
			})

			if (child.data && Object.keys(child.data).length) {
				
				const dataDeleteKeys = [ 'stores', 'ID', 'author', 'config_url', 'content', 'dbid', 'edit_url', 'guid', 'id', 'post_author', 'post_content', 'post_date', 'post_id', 'post_modified', 'post_name', 'post_status', 'post_title', 'post_type', 'collection', 'title', 'titulo', 'url' ]
				dataDeleteKeys.forEach((key, i) => {
					if(key in child.data) (delete child.data[key])
				})

			}

			if (child.meta && Object.keys(child.meta).length) {
				
				const metaDeleteKeys = [ 'stores', 'ID', 'author', 'config_url', 'content', 'dbid', 'edit_url', 'guid', 'id', 'post_author', 'post_content', 'post_date', 'post_id', 'post_modified', 'post_name', 'post_status', 'post_title', 'post_type', 'collection', 'title', 'titulo', 'url' ]
				metaDeleteKeys.forEach((key, i) => {
					if(key in child.meta) (delete child.meta[key])
				})

				Object.keys(child.meta).length && Object.keys(child.meta).forEach((key, i) => {
					if (!child.data) child.data = {}
					child.data[key] = child.meta[key];
				})

				delete child.meta;

			}

			try {

				child.ID && workRef.collection(collection)
					.doc(child.ID.toString())
					.set(child)
					.then(function () {
						// console.log("Success ID: ", child.ID);
						successCounter++;
						counter++;
						counter === numChildren && console.log('', {
							collection,
							successCounter,
							errCounter,
							counter,
							id: childKey,
							time: moment().format(CONSTANTS.MOMENT_ISO),
							lastKey
						});
					})
					.catch(function (error) {
						errIds.push(child.ID)
						errCounter++;
						counter++;
						counter === numChildren && console.log('', {
							collection,
							successCounter,
							errCounter,
							counter,
							id: childKey,
							time: moment().format(CONSTANTS.MOMENT_ISO),
							lastKey
						});
						console.log(" ");
						console.log("Error errCounter:", errCounter);
						console.log("Error errIds:", errIds.toString());
						console.log("Error adding document ID:", child.ID);
						console.error("Error adding document ERROR: ", ((error.toString && error.toString())||error));
						// console.log("child:", child);
					});

			} catch (error) { 
				
				errIds.push(child.ID)
				errCounter++;
				counter++;
				counter === numChildren && console.log('', {
					collection,
					successCounter,
					errCounter,
					counter,
					id: childKey,
					time: moment().format(CONSTANTS.MOMENT_ISO),
					lastKey
				});
				console.log(" ");
				console.log("Catch errCounter:", errCounter);
				console.log("Catch errIds:", errIds.toString());
				console.log("Catch adding document ID:", child.ID);
				console.error("Catch adding document ERROR: ", ((error.toString && error.toString()) || error));
				console.log("child:", child);
				// console.log("child:", JSON.stringify(child));

			}

		})

	}).catch(err=>console.log('DatabaseRef > error en promesa:',err));
}