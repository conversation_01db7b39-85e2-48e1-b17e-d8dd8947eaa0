const yargs = require('yargs/yargs')
const { hideBin } = require('yargs/helpers')
const argv = yargs(hideBin(process.argv)).argv

let env;
switch (argv.env) {
	case 'p': case 'prod': case 'production':
		env = 'production'
	break;	
	case 'e': case 'em': case 'emulator':
		env = 'emulator'
	break;	
	case 'd': case 'dev': case 'development':
		env = 'development'
	break;	
	case undefined:
	default:
		console.log('\n\n\n');		
		console.log(" ---------------- NO ENV DEFINED !!!!! ------------------ ");
		console.log('\n\n\n');
		process.exit()
	return
}

const APP_ENVIROINMENT = env||'development';

// grabHereToCopyPaste
const pagarmeClient = async () => {
    // PAGARME
	const pagarme = require('pagarme');
	const PAGARME_API_KEY = ['dev','development'].includes(APP_ENVIROINMENT) ? "ak_test_9KRGIevO33Bo02hv4yBoXgrYZWCco2" : "**************************************";
	const client = await pagarme.client.connect({ api_key: PAGARME_API_KEY })
	// const PAGARME_API_KEY = functions.config().pagarme.api_key;
	return client;
}

// grabHereToCopyPaste
const createTransaction = async (data) => {

	// PAGARME
	const client = await pagarmeClient();

	let { customer } = data;
				
	if ( customer && customer.document_type ) {         
		let type = customer.document_type;
		let number = customer.document_number;
		if ( !(customer.documents||[]).find(d=>d.number===number) ) {
			customer.documents = [{ type, number }]
		}
		delete data.customer.document_type;
		delete data.customer.document_number;
	}

	return client.transactions.create(data)

}

// grabHereToCopyPaste
const createPlan = async (data) => {

	// PAGARME
	const client = await pagarmeClient();
	if (data.id) {
		delete data.id;
	}
	return client.plans.create(data)

}

// grabHereToCopyPaste
const createSubscription = async (data) => {

	// PAGARME
	const client = await pagarmeClient();

	let { customer } = data;
				
	if ( customer && customer.document_type ) {         
		let type = customer.document_type;
		let number = customer.document_number;
		if ( !(customer.documents||[]).find(d=>d.number===number) ) {
			customer.documents = [{ type, number }]
		}
		delete data.customer.document_type;
		delete data.customer.document_number;
	}

	return client.subscriptions.create(data)

}

// grabHereToCopyPaste
const updateSubscription = async (id, data) => {

	// PAGARME
	const client = await pagarmeClient();

	return client.subscriptions.update({ ...data, id })

}

// grabHereToCopyPaste
const getTransaction = async (id) => {

	// PAGARME
	const client = await pagarmeClient();
	return client.transactions.find({ id })

}

// grabHereToCopyPaste
const getPlan = async (id) => {

	// PAGARME
	const client = await pagarmeClient();
	return client.plans.find({ id })

}

// grabHereToCopyPaste
const getSubscription = async (id) => {

	// PAGARME
	const client = await pagarmeClient();
	return client.subscriptions.find({ id })

}

// grabHereToCopyPaste
const getSubscriptionTransactions = async (id) => {

	// PAGARME
	const client = await pagarmeClient();
	return client.subscriptions.findTransactions({ id })
}


// grabHereToCopyPaste
const cancelSubscription = async (id) => {

	// PAGARME
	const client = await pagarmeClient();
	return client.subscriptions.cancel({ id })

}


module.exports = {
	pagarmeClient,
	getPlan,
	getTransaction,
	getSubscription,
	createPlan,
	createSubscription,
	updateSubscription,
	createTransaction,
	getSubscriptionTransactions,
	cancelSubscription,
}