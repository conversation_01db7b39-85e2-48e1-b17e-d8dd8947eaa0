{"functions": {"predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint"], "source": "functions"}, "database": {"rules": "database.rules.json"}, "storage": {"rules": "storage.rules"}, "emulators": {"functions": {"port": 5001}, "database": {"port": 9000}, "firestore": {"port": 8080}, "pubsub": {"port": 8085}}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "hosting": {"public": "public", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}}