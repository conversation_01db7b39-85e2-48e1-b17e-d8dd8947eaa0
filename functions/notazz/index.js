const { ROLES, CONSTANTS, COLLECTIONS,  FirestoreRef, helpers, moment, momentNow, langMessages } = require('../init');
const axios = require('axios');
const qs = require('qs');

let processing = false;
let stackInterval = [];

const addToNFStack = (apiKey, nfType, nfData) => {
    return new Promise((resolve,reject)=>{
        let i = stackInterval.length;
        stackInterval[i] = setInterval(() => {
            console.log('createNotazzNF > addToNFStack > stackInterval',stackInterval.length);
            if (!processing) {
                clearInterval(stackInterval[i])
                return createNotazzNF(apiKey, nfType, nfData)
                .then(resolve)
                .catch(reject)
            }
            return null
        }, 1000 );
    })
}

const createNotazzNF = (apiKey, nfType, nfData) => {
    
    // console.log('createNotazzNF > data',nfType,nfData);    
    if (processing) return addToNFStack(apiKey, nfType, nfData);
    
    return new Promise((resolve,reject) => {
        processing = true;

        let isService = nfType==="service";
        let action = isService ? CONSTANTS.AJAX_ACTION_CREATE_NOTAZZ_NFSE : CONSTANTS.AJAX_ACTION_CREATE_NOTAZZ_NFE;

        const data = {
            action, 
            nfe: nfData,
            nfse: nfData,
            apiKey,
        }

        const params = qs.stringify(data)
        
        return axios.post(CONSTANTS.WP_AJAX_URL, params)
        .then(response=>{
            const { data, data: { result, status, error } } = response;
            console.log('createNotazzNF > response', { status, data })
            processing = false;
            if ( status === true ) {
                return resolve(result)
            }
            if ( result && result.motivo ) {
                return reject(result.motivo)
            }
            return reject(langMessages["nf.apiError"])
        })
        .catch(err=>{
            console.log('createNotazzNF > err',err);
            processing = false;
            return reject(err)
        })

    })

}

module.exports = {
    createNotazzNF
}