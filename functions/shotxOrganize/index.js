const axios = require("axios");
const { COLLECTIONS, CONSTANTS, moment } = require("../init");
const { getLeadById } = require("../leads");
const { fetchCollection } = require("../post");
const { replaceShortCodes } = require("../shortcodes");
const { getQuickMessages } = require("../utils/quickMessages");
const { saveMessage, saveScheduledMessage } = require("../utils/redisClient");

/**
 * Organiza e prepara mensagens para serem salvas no Redis
 * @param {Array} appointments - Lista de agendamentos para processar
 * @returns {Promise<void>}
 */
const shotxOrganizeMessages = async (appointments) => {
  if (!appointments || appointments.length === 0) {
    console.log(
      "SHOTXCRON > SHOTXORGANIZE > ORGANIZE MESSAGES > NO SCHEDULES TO PROCESS"
    );
    return;
  }

  // Usa Promise.all para processar os agendamentos em paralelo
  await Promise.all(
    appointments.map((appointment) => {
      if (appointment.type === "sniper") {
        const url = `${process.env.CHAT_API_URL}/sniper/broadcast`;

        axios
          .post(url, {
            shotxCrons: appointment,
          })
          .catch((error) => {
            console.log("SHOTXCRON > SEND TO CHAT API > ERROR", error);
          });
      }
      prepareMessageAndSaveInRedis(appointment);
    })
  );
};

/**
 * Prepara mensagens para cada contato e salva no Redis
 * @param {Object} appointment - Objeto de agendamento
 * @returns {Promise<Array>} - Lista de mensagens salvas
 */
const prepareMessageAndSaveInRedis = async (appointment) => {
  try {
    if (
      !appointment ||
      !appointment.contacts ||
      appointment.contacts.length === 0
    ) {
      console.log(
        "SHOTXCRON > SHOTXORGANIZE >  PREPARE MESSAGES > No contacts in appointment",
        appointment && appointment.id ? appointment.id : "unknown"
      );
      return [];
    }

    console.log(
      `SHOTXCRON > SHOTXORGANIZE > PREPARE MESSAGES > Processing appointment ${appointment.id} with ${appointment.contacts.length} contacts`
    );

    const savedMessages = [];
    const messagePromises = [];

    let index = 0;
    for (const contact of appointment.contacts) {
      const time = prepareTime(
        appointment.scheduled_date,
        appointment.intervalUnit,
        index
      );

      if (!time) {
        console.error(
          `SHOTXCRON > SHOTXORGANIZE > PREPARE MESSAGES > Invalid time for contact ${contact.id}`
        );
        index = index + (appointment.intervalQty || 1);
        continue;
      }

      // Criar uma função para processar cada contato
      const processContact = async () => {
        try {
          // Preparar a mensagem com shortcodes substituídos
          const messagePrepared = await prepareMessage(appointment, contact);

          if (!messagePrepared) {
            console.log(
              `SHOTXCRON > SHOTXORGANIZE > PREPARE MESSAGES > Failed to prepare message for contact ${contact.id}`
            );
            return null;
          }

          console.log(
            `SHOTXCRON > SHOTXORGANIZE > PREPARED MESSAGES > CONTENT:`,
            messagePrepared.content
          );
          // Criar objeto de mensagem com tempo
          const messageWithTime = {
            message: messagePrepared || appointment.message,
            scheduled_date: time,
            attempts: 0,
            contact_id: contact.id,
            contact_name: contact.contactName,
            contact_remote_id: contact.contactRemoteId,
            shotxCronId: appointment.id,
            instanceId: appointment.instance.id,
            accountId: appointment.instance.accountId,
            shotxCron: appointment,
            created_at: new Date().toISOString(),
          };

          // Gerar ID único para a mensagem
          const messageId = `${appointment.id}_${contact.id}_${Date.now()}`;

          // Salvar no Redis
          const saved = await saveMessageInRedis(
            messageId,
            messageWithTime,
            time
          );

          if (saved) {
            console.log(
              `SHOTXCRON > SHOTXORGANIZE > PREPARE MESSAGES > Saved message for contact ${contact.id}`
            );
            return { ...messageWithTime, id: messageId };
          }
          return null;
        } catch (error) {
          console.error(
            `SHOTXCRON > SHOTXORGANIZE > PREPARE MESSAGES > Error processing contact ${contact.id}:`,
            error
          );
          return null;
        }
      };

      // Adicionar à lista de promessas
      messagePromises.push(processContact());

      // Incrementar o índice para o próximo contato
      index = index + (appointment.intervalQty || 1);
    }

    // Aguardar todas as promessas e filtrar resultados nulos
    const results = await Promise.all(messagePromises);
    savedMessages.push(...results.filter(Boolean));

    console.log(
      `SHOTXCRON > SHOTXORGANIZE > PREPARE MESSAGES > Successfully saved ${savedMessages.length} messages for appointment ${appointment.id}`
    );
    return savedMessages;
  } catch (error) {
    console.error(
      `SHOTXCRON > SHOTXORGANIZE > PREPARE MESSAGES > Error processing appointment ${appointment && appointment.id ? appointment.id : "unknown"}:`,
      error
    );
    return [];
  }
};

/**
 * Calcula o tempo agendado com base na data, unidade de intervalo e índice
 * @param {string} date - Data base
 * @param {string} intervalUnit - Unidade de intervalo (mm, hh, dd)
 * @param {number} index - Índice para cálculo do intervalo
 * @returns {string|null} - Data formatada ou null em caso de erro
 */
const prepareTime = (date, intervalUnit, index) => {
  // Validar parâmetros
  if (!date || !intervalUnit) {
    console.error(
      "SHOTXCRON > PREPARE TIME > ERROR: Missing date or intervalUnit"
    );
    return null;
  }

  if (index === undefined || index === null) {
    console.error("SHOTXCRON > PREPARE TIME > ERROR: Missing index");
    return null;
  }

  try {
    // Criar uma cópia da data para não modificar a original
    console.log(
      "SHOTXCRON > SHOTXORGANIZE > PREPARE MESSAGES > START PREPARETIME"
    );

    const momentDate = moment(date).clone();

    if (!momentDate.isValid()) {
      console.error(
        `SHOTXCRON > PREPARE TIME > ERROR: Invalid date format: ${date}`
      );
      return null;
    }

    // Normalizar a unidade de intervalo para minúscula
    const unit = intervalUnit.toLowerCase();

    // Mapear unidades de intervalo para unidades do moment
    const unitMap = {
      mm: "minutes",
      hh: "hours",
      dd: "days",
      minutos: "minutes",
      horas: "hours",
      dias: "days",
    };

    // Verificar se a unidade é válida
    if (!unitMap[unit]) {
      console.error(
        `SHOTXCRON > PREPARE TIME > ERROR: Invalid intervalUnit: ${intervalUnit}`
      );
      return null;
    }

    // Adicionar o intervalo e formatar a data
    const updatedDate = momentDate
      .add(index, unitMap[unit])
      .format(CONSTANTS.MOMENT_ISO);
    console.log(`SHOTXCRON > PREPARE TIME > UPDATEDDATE`, updatedDate);
    return updatedDate;
  } catch (error) {
    console.error(`SHOTXCRON > PREPARE TIME > ERROR: ${error.message}`);
    return null;
  }
};

/**
 * Prepara a mensagem substituindo shortcodes com dados do lead
 * @param {Object} appointment - Objeto de agendamento
 * @param {Object} contact - Objeto de contato
 * @returns {Promise<Object|null>} - Objeto de mensagem preparada ou null em caso de erro
 */
const prepareMessage = async (appointment, contact) => {
  if (!appointment || !appointment.message) {
    console.error(
      "SHOTXCRON > PREPARE MESSAGE > Missing appointment or message"
    );
    return null;
  }

  if (!contact || !contact.id) {
    console.error(
      "SHOTXCRON > PREPARE MESSAGE > Missing contact or contact ID"
    );
    return null;
  }

  try {
    // Buscar dados do lead
    const lead = await getLeadById(contact.id);

    if (!lead) {
      console.error(
        `SHOTXCRON > PREPARE MESSAGE > Lead not found for contact ID: ${contact.id}`
      );
      return null;
    }

    // Substituir shortcodes na mensagem
    const messageContent = await replaceShortCodes(
      appointment.message,
      [],
      COLLECTIONS.LEADS_COLLECTION_NAME,
      [lead]
    );

    if (!messageContent) {
      console.warn(
        `SHOTXCRON > PREPARE MESSAGE > Empty message content after shortcode replacement for contact ${contact.id}`
      );
      // Usar a mensagem original como fallback
      return {
        content: appointment.message,
        leadId: contact.id,
        appointmentId: appointment.id,
        leadData: {
          name: lead.displayName || lead.name || "",
          email: lead.email || "",
          phone: lead.mobile || "",
        },
      };
    }

    // Retornar objeto com a mensagem preparada e dados adicionais
    return {
      content: messageContent.content,
      leadId: contact.id,
      appointmentId: appointment.id,
      leadData: {
        name: lead.displayName || lead.name || "",
        email: lead.email || "",
        phone: lead.mobile || "",
      },
    };
  } catch (error) {
    console.error(
      `SHOTXCRON > PREPARE MESSAGE > Error preparing message for contact ${contact.id}:`,
      error
    );
    return null;
  }
};

/**
 * Salva uma mensagem no Redis com um timestamp para agendamento
 * @param {string} messageId - ID único da mensagem
 * @param {Object} message - Objeto da mensagem
 * @param {string} time - Data/hora agendada em formato ISO
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const saveMessageInRedis = async (messageId, message, time) => {
  try {
    // Validar parâmetros
    if (!messageId) {
      console.error(
        "SHOTXCRON > SHOTXORGANIZE > SAVE MESSAGE > ERROR: Message ID is required"
      );
      return false;
    }

    if (!message) {
      console.error(
        "SHOTXCRON > SHOTXORGANIZE > SAVE MESSAGE > ERROR: Message content is required"
      );
      return false;
    }

    // Gerar chave única para a mensagem
    const messageKey = `shotx:message:${messageId}`;

    // Chave para a lista ordenada de mensagens agendadas
    const scheduledListKey = "shotx:scheduled_messages";

    // Calcular o timestamp para agendamento
    let scheduledTime;

    if (time) {
      // Converter a data agendada para timestamp (em segundos)
      scheduledTime = new Date(time).getTime();
    } else {
      // Usar o timestamp atual + 60 segundos como fallback
      scheduledTime = Math.floor(Date.now() / 1000) + 60;
    }

    // Adicionar metadados à mensagem
    const enrichedMessage = {
      ...message,
      id: messageId,
      _scheduled_timestamp: scheduledTime,
      _scheduled_iso: time || new Date(scheduledTime).toISOString(),
      _created_at: new Date().toISOString(),
    };

    // Salvar a mensagem na lista ordenada
    const result = await saveScheduledMessage(
      scheduledListKey,
      scheduledTime,
      messageKey,
      enrichedMessage
    );

    if (result) {
      console.log(
        `SHOTXCRON > SAVE MESSAGE > SUCCESS: Message saved with key ${messageKey} for time ${time}`
      );
    } else {
      console.error(
        `SHOTXCRON > SAVE MESSAGE > ERROR: Failed to save message with key ${messageKey}`
      );
    }

    return result;
  } catch (error) {
    console.error("SHOTXCRON > SAVE MESSAGE > ERROR:", error);
    console.error(error.stack);
    return false;
  }
};
module.exports = {
  shotxOrganizeMessages,
  prepareTime,
};
