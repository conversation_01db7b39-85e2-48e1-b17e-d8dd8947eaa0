
// constans
const FACEBOOK_QIPLUSAPP_SECRET = "********************************";
const FACEBOOK_QIPLUSAPP_ID = "436576064542288";
const FACEBOOK_QIPLUSAPP_TOKEN = "436576064542288|KQ_2HHwMh9-oy19UIwBfZD7L59k";
const FACEBOOK_QIPLUSAPP_USER_TOKEN = "EAAGNEEkZBllABAKL00vYloffHIwinKjmkgskZAxcGUd9Rxtbm6Q4oFXQ41CEAKxhZBNk48FZAZCQfTXyfRDBNQLn7CqhEodAj77tFEtWWFk8nemrOUS0l33cRdhWHrSGxLrE3hRetZCKMel6ZBcFnn83Yu8pZB9a6Cua1lrkVv863SqGviNwOdZCUh0k6VxOfo6QZD";

const app_secret = FACEBOOK_QIPLUSAPP_SECRET;
const app_id = FACEBOOK_QIPLUSAPP_ID;
const app_token = FACEBOOK_QIPLUSAPP_TOKEN;
const app_user_token = FACEBOOK_QIPLUSAPP_USER_TOKEN;
    
const showDebugingInfo = true; 

const bizSdk = require('facebook-nodejs-business-sdk');

const Content = bizSdk.Content;
const CustomData = bizSdk.CustomData;
const DeliveryCategory = bizSdk.DeliveryCategory;
const EventRequest = bizSdk.EventRequest;
const UserData = bizSdk.UserData;
const ServerEvent = bizSdk.ServerEvent;

const actionSources = ['email', 'website', 'phone_call', 'chat', 'physical_store', 'system_generated', 'other'];
const defaultActionSource = 'other';

/**
 * 
 * Standard Events
 * https://developers.facebook.com/docs/facebook-pixel/reference#standard-events
 *
*/
const eventNames = [
    'AddPaymentInfo',			// When payment information is added in the checkout flow.
    'AddToCart',				// When a product is added to the shopping cart.
    'AddToWishlist',			// When a product is added to a wishlist.
    'CompleteRegistration',		// When a registration form is completed.
    'Contact',					// When a person initiates contact with your business via telephone, SMS, email, chat, etc.
    'CustomizeProduct',			// When a person customizes a product.
    'Donate',					// When a person donates funds to your organization or cause.
    'FindLocation',				// When a person searches for a location of your store via a website or app, with an intention to visit the physical location.
    'InitiateCheckout',			// When a person enters the checkout flow prior to completing the checkout flow.
    'Lead',						// When a sign up is completed.
    'PageView',					// This is the default pixel tracking page visits.
    'Purchase',					// When a purchase is made or checkout flow is completed.
    'Schedule',					// When a person books an appointment to visit one of your locations.
    'Search',					// When a search is made.
    'StartTrial',				// When a person starts a free trial of a product or service you offer.
    'SubmitApplication',		// When a person applies for a product, service, or program you offer.
    'Subscribe',				// When a person applies to a start a paid subscription for a product or service you offer.
    'ViewContent',				// A visit to a web page you care about (for example, a product page or landing page). ViewContent tells you if someone visits a web page's URL, but not what they see or do on that page.
];

const sendEventRequest = (eventData, access_token, pixel_id) => {

    // const access_token = '<ACCESS_TOKEN>';
    // const pixel_id = '<ADS_PIXEL_ID>';
    const api = bizSdk.FacebookAdsApi.init(access_token);

    return new Promise((resolve,reject)=>{
        
        let { 
            eventName, 
            eventSourceUrl, 
            actionSource, 
            userData, 
            productData, 
            remoteAddress, 
            userAgent,
            fbp,
            fbc
        } = eventData;

        actionSource = actionSources.includes(actionSource) ? actionSource : defaultActionSource;

        let current_timestamp = Math.floor(new Date() / 1000);

        let serverEvent = (new ServerEvent())
                        .setEventName(eventName)
                        .setEventTime(current_timestamp)
                        .setEventSourceUrl(eventSourceUrl)
                        .setActionSource(actionSource);
        
        // userData
        let uData = new UserData();

        // It is recommended to send Client IP and User Agent for Conversions API Events.
        if (remoteAddress) uData = uData.setClientIpAddress(remoteAddress)
        if (userAgent) uData = uData.setClientUserAgent(userAgent)
        if (fbp) uData = uData.setFbp(fbp)
        if (fbc) uData = uData.setFbc(fbc)

        if ( userData ) {

            let { 
                email, 
                phones,
                firstName,
                lastName,
                dateOfBirth,
                gender,
                zip,
                city,
                state,
                country,
                externalId,                
            } = userData;
                           
            if (email) uData = uData.setEmail(email)
            if (firstName) uData = uData.setFirstName(firstName)
            if (lastName) uData = uData.setLastName(lastName)
            if (dateOfBirth) uData = uData.setDateOfBirth(dateOfBirth)
            if (gender) uData = uData.setGender(gender)
            if (zip) uData = uData.setZip(zip)
            if (city) uData = uData.setCity(city)
            if (state) uData = uData.setState(state)
            if (country) uData = uData.setCountry(country)
            if (externalId) uData = uData.setExternalId(externalId)

            // .setPhones(['12345678901', '14251234567'])
            if (Array.isArray(phones) && phones.length) uData = uData.setPhones(phones)
    
        }

        serverEvent = serverEvent.setUserData(userData)
                        
        if ( productData ) {
            
            let { currency, value, productId, quantity } = productData;

            let content = new Content().setId(productId).setQuantity(parseFloat(quantity))
                            // .setDeliveryCategory(DeliveryCategory.HOME_DELIVERY);
            
            let customData = new CustomData().setContents([content])

            if (currency) customData = customData.setCurrency(currency)
            if (value) customData = customData.setValue(value)
            
            serverEvent = serverEvent.setCustomData(customData)

        }
        
        const eventsData = [serverEvent];
        const eventRequest = (new EventRequest(access_token, pixel_id)).setEvents(eventsData);
        
        eventRequest
        .execute()
        .then(response => {
            console.log('Response: ', response);
            return resolve(response)
        }, err => {
            console.error('Facebook SDK > Error callback: ', err);
            return reject(err)
        }).catch(err=>{
            console.error('Facebook SDK > Error catch: ', err);
            return reject(err)
        })

    })
    
}

const getLeadsByAdId = (ad_group_id, access_token) => {

    const Ad = bizSdk.Ad;
    const Lead = bizSdk.Lead;
    
    const api = bizSdk.FacebookAdsApi.init(access_token);

    if (showDebugingInfo) {
      api.setDebug(true);
    }    
    
    let fields, params;
    fields = [];
    params = {};

    const leadsAdsRequest = new Ad(ad_group_id);

    return leadsAdsRequest.getLeads(
        fields,
        params
    );
    

}

const getLeadsByFormId = (form_id, access_token) => {

    const LeadgenForm = bizSdk.LeadgenForm;
    const Lead = bizSdk.Lead;
    
    const api = bizSdk.FacebookAdsApi.init(access_token);

    if (showDebugingInfo) {
      api.setDebug(true);
    }    
    
    let fields, params;
    fields = ['created_time','id','ad_id','form_id','field_data'];
    params = {};

    const leadsAdsRequest = new LeadgenForm(form_id);

    return leadsAdsRequest.getLeads(
        fields,
        params
    );
    

}

const getLead = (lead_id, access_token) => {

    const Lead = bizSdk.Lead;
    const api = bizSdk.FacebookAdsApi.init(access_token);
    
    if (showDebugingInfo) {
        api.setDebug(true);
    }

    let fields, params;

    fields = [];
    params = {};
    
    const leadRequest = new Lead(lead_id)

    return leadRequest.get(fields, params)

}
module.exports = {
    app_secret,
    app_id,
    app_token,
    app_user_token,
    sendEventRequest,
    getLeadsByAdId,
    getLeadsByFormId,
    getLead,
}