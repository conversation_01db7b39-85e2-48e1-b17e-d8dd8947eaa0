const { FirestoreRef, functions, helpers, cors, moment, momentNow, langMessages, ERROR_TYPES } = require('../init');
const { ROLES, CONSTANTS, COLLECTIONS } = require('../init');
const { fetchPost, addNewPost, addNewLog } = require('../post');
const { adminMail } = require('../mailing');
const { createNotazzNF } = require('../notazz');

const { convertTonumOnly, jsonClone, nowToISO, replaceUndefined } = helpers;

const qiplusNFModel = require('../notazz/qiplus_nfse');

const APP_ENVIROINMENT = functions.config().qiplus.env;

const {
	CRONJOB_TYPES,
	CRONJOB_SUBJECTS,
	MOMENT_ISO,
} = CONSTANTS;

// grabHereToCopyPaste
const pagarmeClient = async () => {
	// PAGARME
	const pagarme = require('pagarme');
	const PAGARME_API_KEY = APP_ENVIROINMENT === 'development' ? "ak_test_9KRGIevO33Bo02hv4yBoXgrYZWCco2" : "**************************************";
	const client = await pagarme.client.connect({ api_key: PAGARME_API_KEY })
	// const PAGARME_API_KEY = functions.config().pagarme.api_key;
	return client;
}

// grabHereToCopyPaste
const createTransaction = async (data) => {

	// PAGARME
	const client = await pagarmeClient();

	let { customer } = data;
				
	if ( customer && customer.document_type ) {         
		let type = customer.document_type;
		let number = customer.document_number;
		if ( !(customer.documents||[]).find(d=>d.number===number) ) {
			customer.documents = [{ type, number }]
		}
		delete data.customer.document_type;
		delete data.customer.document_number;
	}

	return client.transactions.create(data)

}

// grabHereToCopyPaste
const createPlan = async (data) => {

	// PAGARME
	const client = await pagarmeClient();
	if (data.id) {
		delete data.id;
	}
	return client.plans.create(data)

}

// grabHereToCopyPaste
const createSubscription = async (data) => {

	// PAGARME
	const client = await pagarmeClient();

	let { customer } = data;
				
	if ( customer && customer.document_type ) {         
		let type = customer.document_type;
		let number = customer.document_number;
		if ( !(customer.documents||[]).find(d=>d.number===number) ) {
			customer.documents = [{ type, number }]
		}
		delete data.customer.document_type;
		delete data.customer.document_number;
	}

	return client.subscriptions.create(data)

}

// grabHereToCopyPaste
const updateSubscription = async (id, data) => {

	// PAGARME
	const client = await pagarmeClient();

	return client.subscriptions.update({ ...data, id })

}

// grabHereToCopyPaste
const getPlan = async (id) => {

	// PAGARME
	const client = await pagarmeClient();
	return client.plans.find({ id })

}

// grabHereToCopyPaste
const getTransaction = async (id) => {

	// PAGARME
	const client = await pagarmeClient();
	return client.transactions.find({ id })

}

// grabHereToCopyPaste
const getSubscription = async (id) => {

	// PAGARME
	const client = await pagarmeClient();
	return client.subscriptions.find({ id })

}

// grabHereToCopyPaste
const getSubscriptionTransactions = async (id) => {

	// PAGARME
	const client = await pagarmeClient();
	return client.subscriptions.findTransactions({ id })
}

// grabHereToCopyPaste
const cancelSubscription = async (id) => {

	// PAGARME
	const client = await pagarmeClient();
	return client.subscriptions.cancel({ id })

}

// grabHereToCopyPaste
const pagarmeApi = {
	createTransaction: async (request, response) => {
		cors(request, response, async () => { 
			const postData = request.body.data;
			console.log('createTransaction > postData',postData);
	
			if (!postData) {
				// throw new functions.https.HttpsError('failed-precondition', 'No data sent' + JSON.stringify(request.body));
				console.log('failed-precondition No data sent', request.body);
				return null;
			}
			
			return createTransaction(postData)
			.then(transaction => {
				return response.send({ data: { transaction }})
			}).catch(error=>{
				const errorType = ERROR_TYPES.ERROR_ADDING_TRANSACTION;
				let errorMessage = '';
				try {
					errorMessage = error.response.errors[0].message;
				} catch (err) { console.log(err) }
				response.send({ data: { error: errorType, errorMessage, errorType, debug: error }})
			})
			
		});
		return null;
	
	},
	createSubscription: async (request, response) => {
		cors(request, response, async () => { 
			const postData = request.body.data;
			console.log('createSubscription > postData',postData);
	
			if (!postData) {
				// throw new functions.https.HttpsError('failed-precondition', 'No data sent' + JSON.stringify(request.body));
				console.log('failed-precondition No data sent', request.body);
				return null;
			}
			
			return createSubscription(postData)
			.then(subscription => {
				return response.send({ data: { subscription }})
			}).catch(error=>{
				const errorType = ERROR_TYPES.ERROR_ADDING_SUBSCRIPTION;
				let errorMessage = '';
				try {
					errorMessage = error.response.errors[0].message;
				} catch (err) { console.log(err) }
				response.send({ data: { error: errorType, errorMessage, errorType, debug: error }})
			})
			
		});
		return null;
	
	},
	getSubscription: async (request, response) => {
		cors(request, response, async () => { 
			const postData = request.body.data;
			console.log('getSubscription > postData',postData);
	
			if (!postData || !postData.id) {
				// throw new functions.https.HttpsError('failed-precondition', 'No data sent' + JSON.stringify(request.body));
				console.log('failed-precondition No data sent', request.body);
				return null;
			}
			
			const { id } = postData;
	
			return getSubscription(id)
			.then(subscription => {
				return response.send({ data: { subscription }})
			}).catch(error=>{
				const errorType = ERROR_TYPES.ERROR_GETTING_SUBSCRIPTION;
				let errorMessage = '';
				try {
					errorMessage = error.response.errors[0].message;
				} catch (err) { console.log(err) }
				response.send({ data: { error: errorType, errorMessage, errorType, debug: error }})
			})
	
		});
		return null;
	
	},
	getSubscriptionTransactions: async (request, response) => {
		cors(request, response, async () => { 
			const postData = request.body.data;
			console.log('getSubscriptionTransactions > postData',postData);
	
			if (!postData || !postData.id) {
				// throw new functions.https.HttpsError('failed-precondition', 'No data sent' + JSON.stringify(request.body));
				console.log('failed-precondition No data sent', request.body);
				return null;
			}
			
			const { id } = postData;
	
			return getSubscriptionTransactions(id)
			.then(subscription => {
				return response.send({ data: { subscription }})
			}).catch(error=>{
				const errorType = ERROR_TYPES.ERROR_GETTING_SUBSCRIPTION;
				let errorMessage = '';
				try {
					errorMessage = error.response.errors[0].message;
				} catch (err) { console.log(err) }
				response.send({ data: { error: errorType, errorMessage, errorType, debug: error }})
			})
	
		});
		return null;
	
	},
	cancelSubscription: async (request, response) => {
		cors(request, response, async () => { 
			const postData = request.body.data;
			console.log('cancelSubscription > postData',postData);
	
			if (!postData || !postData.id) {
				// throw new functions.https.HttpsError('failed-precondition', 'No data sent' + JSON.stringify(request.body));
				console.log('failed-precondition No data sent', request.body);
				return null;
			}
			
			const { id } = postData;
	
			return cancelSubscription(id)
			.then(subscription => {
				return response.send({ data: { subscription }})
			}).catch(error=>{
				const errorType = ERROR_TYPES.ERROR_CANCELING_SUBSCRIPTION;
				let errorMessage = '';
				try {
					errorMessage = error.response.errors[0].message;
				} catch (err) { console.log(err) }
				response.send({ data: { error: errorType, errorMessage, errorType, debug: error }})
			})
	
		});
		return null;
	
	},
	getTransaction: async (request, response) => {
		cors(request, response, async () => { 
			const postData = request.body.data;
			console.log('getTransaction > postData',postData);
	
			if (!postData || !postData.id) {
				// throw new functions.https.HttpsError('failed-precondition', 'No data sent' + JSON.stringify(request.body));
				console.log('failed-precondition No data sent', request.body);
				return null;
			}
			
			const { id } = postData;
	
			return getTransaction(id)
			.then(transaction => {
				return response.send({ data: { transaction }})
			}).catch(error=>{
				const errorType = ERROR_TYPES.ERROR_GETTING_TRANSACTION;
				let errorMessage = '';
				try {
					errorMessage = error.response.errors[0].message;
				} catch (err) { console.log(err) }
				response.send({ data: { error: errorType, errorMessage, errorType, debug: error }})
			})
	
		});
		return null;
	
	}
}

const createTransactionInvoice = async ({ transaction, account, accountOwner, description }) => {
   
   	const { billing_data } = account;
   	const accountId = account.ID;
   	const invoicesRef = FirestoreRef.collection(COLLECTIONS.ACCOUNTS_COLLECTION_NAME).doc(accountId).collection(COLLECTIONS.INVOICES_SUBCOLLECTION_NAME);
   
   	if ( transaction && transaction.status==="paid" ) {

		const invoiceCheck = await invoicesRef.where('transaction_id',"==",transaction.id).get();

		if ( invoiceCheck.size ) {
			return null
		}

		if (!accountOwner) {
			accountOwner = account[CONSTANTS.OWNER_FIELD] ? await fetchPost(COLLECTIONS.QIUSERS_COLLECTION_NAME, account[CONSTANTS.OWNER_FIELD]) : {}
		}

		let nfModel = jsonClone(qiplusNFModel);
		let notazzNF = { ...nfModel };
		
		notazzNF.DOCUMENT_BASEVALUE = (parseFloat(transaction.paid_amount||transaction.amount)/100).toFixed(2);

		/* customer */
		const { address, customer, phone } = billing_data;
		let { type, email, name, document_number } = customer;
		
		notazzNF.DESTINATION_TAXTYPE = type==='individual'?'F':'J';
		notazzNF.DESTINATION_NAME = `${name}`;
		notazzNF.DESTINATION_TAXID = `${convertTonumOnly(document_number)}`;
		notazzNF.DOCUMENT_DESCRIPTION = `${description||qiplusNFModel.DOCUMENT_DESCRIPTION}`;

		if ( email ) {
			notazzNF.DESTINATION_EMAIL = `${email}`;
		}
		if ( phone.ddd && phone.number ) {
			notazzNF.DESTINATION_PHONE = `${convertTonumOnly(`${phone.ddi}${phone.ddd}${phone.number}`)}`;
		}
		if ( accountOwner.IE ) {
			notazzNF.DESTINATION_IE = `${convertTonumOnly(accountOwner.IE)}`;
		}
		if ( accountOwner.IM ) {
			notazzNF.DESTINATION_IM = `${convertTonumOnly(accountOwner.IM)}`;
		}

		/* address */
		if ( address.street ) {
			notazzNF.DESTINATION_STREET = `${address.street}`;
		}
		if ( address.street_number ) {
			notazzNF.DESTINATION_NUMBER = `${address.street_number}`;
		}
		if ( address.complementary ) {
			notazzNF.DESTINATION_COMPLEMENT = `${address.complementary}`;
		}
		if ( address.neighborhood ) {
			notazzNF.DESTINATION_DISTRICT = `${address.neighborhood}`;
		}
		if ( address.city ) {
			notazzNF.DESTINATION_CITY = `${address.city}`;
		}
		if ( address.state ) {
			notazzNF.DESTINATION_UF = `${address.state}`;
		}
		if ( address.zipcode ) {
			notazzNF.DESTINATION_ZIPCODE = `${convertTonumOnly(address.zipcode)}`;
		}
		
		/* EXTERNAL_ID */
		notazzNF.EXTERNAL_ID = transaction.id;
		notazzNF.SALE_ID = transaction.id;

		Object.keys(nfModel).forEach(k=>{
		const v = (k in notazzNF) ? notazzNF[k] : nfModel[k];
			notazzNF[k] = v;
		})
		
		const apiKey = CONSTANTS.NOTAZZ_QIPLUS_API_KEY;
		const nfType = "service";

		console.log('createTransactionInvoice > notazzNF', { apiKey, notazzNF });
		
		return createNotazzNF(apiKey, nfType, notazzNF)
		.then(result=>{
			console.log('createTransactionInvoice > result', { result });
			return invoicesRef.add({
				collection: COLLECTIONS.INVOICES_SUBCOLLECTION_NAME,
				date: momentNow().format(CONSTANTS.MOMENT_ISO),
				transaction_id: transaction.id,
				nf_ID: result.id,
				nf_error: "",
				invoice: { ...notazzNF }
			})
		})
		.catch(async error=> {
			console.error('createTransactionInvoice > error', { error });
			let msg = "Erro ao gerar a Nota Fiscal";
			if (typeof error === "string") msg = error;

			const emailData = { 
				message: { 
					to: CONSTANTS.WEBMASTER_EMAIL, 
					subject: 'Error log', 
					html: `<div>Ocorreu um erro na geração da nota fiscal: <b>${msg}</b><br><br>
					At: ${nowToISO()}
					<br> ${JSON.stringify(notazzNF)}
					</div>`
				} 
			};
			await adminMail(emailData);
			
			const notification = {
				accountId: CONSTANTS.WEBMASTER_ACCOUNT_ID,
				owner: CONSTANTS.WEBMASTER_ID,
				qiuser: CONSTANTS.WEBMASTER_ID,
				title: "Erro ao gerar a Nota Fiscal",
				message: msg,
				url: `/${COLLECTIONS.ACCOUNTS_COLLECTION_NAME}/${accountId}`,
				context: {
					collection: COLLECTIONS.ACCOUNTS_COLLECTION_NAME,
					id: accountId,
				},
				data: { notazzNF },
				viewed: false,
				scheduled_date: momentNow().format(MOMENT_ISO),
				date: momentNow().format(MOMENT_ISO),
				modified: momentNow().format(MOMENT_ISO),
			}
			await addNewPost(COLLECTIONS.DESKTOP_NOTIFICATIONS_COLLECTION_NAME, notification);
			
		})
	}

	return null

}

const billingCron = () => {
	
	let now = momentNow().format(MOMENT_ISO)
	console.log('billingCron > now',{ now});

    const fetchCronjobs = () => {
        return new Promise((res,rej)=>{
			return FirestoreRef.collection(COLLECTIONS.CRONJOBS_COLLECTION_NAME)
			.where("type","==",CRONJOB_TYPES.BILLING_SCHEDULE)
			.where("executed","==",false)
			.where("scheduled_date","<=",now).get().then(snapshot=>{
				
                let promises = []
                snapshot.forEach(doc=>{
					const job = doc.data();
					const jobId = doc.id;
					const { api, data, context } = job;
					switch (api) {
						case CRONJOB_SUBJECTS.INVOICE :
							promises.push(new Promise(async resolve=>{
								
								const { subscription_id, transaction_id, description, accountId, ownerId } = data;
								const account = await fetchPost(COLLECTIONS.ACCOUNTS_COLLECTION_NAME, accountId, true)
								const accountOwner = await fetchPost(COLLECTIONS.QIUSERS_COLLECTION_NAME, ownerId, true)
								
								let innerPromises = [];
								
								if ( transaction_id ) {
									try {
										const transaction = await getTransaction(transaction_id);
										console.log('billingCron > invoice > transaction', { transaction });
										if ( transaction.status==="paid" ) {
											innerPromises.push(await createTransactionInvoice({ transaction, account, accountOwner, description }))
										}
									} catch (error) {
										console.error('billingCron > invoice > transaction > error', { error });
									}
								}

								if ( subscription_id ) {

									try {
										
										const invoicesRef = FirestoreRef.collection(COLLECTIONS.ACCOUNTS_COLLECTION_NAME).doc(accountId).collection(COLLECTIONS.INVOICES_SUBCOLLECTION_NAME);
										const invoicesSnap = await invoicesRef.get();
										const invoices = invoicesSnap.docs.map(d=>d.data())

										console.log('billingCron > invoice > findTransactions > invoices', { invoices });
										
										const transactions = await getSubscriptionTransactions(subscription_id);

										console.log('billingCron > invoice > findTransactions > transactions', { transactions });

										const txPromises = transactions
										.filter(tx=>tx.status==="paid" && !invoices.find(i=>i.transaction_id===tx.id))
										.map((transaction,i) => new Promise(async txR=>{
											let tx = await createTransactionInvoice({ transaction, account, accountOwner, description })
											return txR(tx)
										}));

										innerPromises.push(...txPromises)

									} catch (error) {
										console.error('billingCron > invoice > findTransactions > error', { error });
									}

									try {
										const endedStatuses = ['canceled','ended'];
										const subscription = await getSubscription(subscription_id);
	
										const { plan, plan: { days }, current_transaction } = subscription;
										
										// console.log('billingCron > invoice > current_transaction', { current_transaction });
										
										if ( !endedStatuses.includes(subscription.status) ) {
	
											let scheduled_moment = momentNow().set({ hour:0, minute:0, second:0, millisecond:0 }).add(days,'days')
											let scheduled_date = scheduled_moment.format(MOMENT_ISO)
	
											await addNewPost(COLLECTIONS.CRONJOBS_COLLECTION_NAME, {
												api: CRONJOB_SUBJECTS.INVOICE,
												executed: false,
												execution_date: "",
												scheduled_date,
												date: momentNow().format(MOMENT_ISO),
												modified: momentNow().format(MOMENT_ISO),
												type: CRONJOB_TYPES.BILLING_SCHEDULE,
												data: { 
													description: `${langMessages["payment.subscription"]} ${plan.name}`,
													subscription_id,
													accountId, 
													ownerId
												},
												context,
											})
											
										}
									} catch (error) {
										console.error('billingCron > invoice > current_transaction > error', { error });
									}
								}
								
								const results = replaceUndefined(await Promise.all(innerPromises))

								await doc.ref.update({ results });
								
								return resolve(results)
							}))
						break;
						case CRONJOB_SUBJECTS.TRANSACTION :
							promises.push(new Promise(async resolve=>{
								let transaction = await createTransaction(data);
								let result = await doc.ref.update({ results: { transaction } });
								return resolve(transaction)
							}))
						break;
						case CRONJOB_SUBJECTS.RENEWAL :
							promises.push(new Promise(async resolve=>{
								let { metadata: { accountId } } = data;
								if ( !accountId ) accountId = context.collection===COLLECTIONS.ACCOUNTS_COLLECTION_NAME ? context.id : null
								try {
									
									const transactionsPaidStatuses = ['authorized', 'paid'];
									const activeStatuses = ['paid','trialing'];
									
									let accountDoc = await FirestoreRef.collection(COLLECTIONS.ACCOUNTS_COLLECTION_NAME).doc(`${accountId}`).get();
									let accountRef = accountDoc.ref;
									let account = accountDoc.data();

									if (!account || !(account.config||{}).billing) {
										return resolve({ error: 'Account billing is not active' })
									}
									
									let { pagarme: { subscription: { id, payment_method, card, plan } } } = account;
									let { billing_data: { address, customer, customer: { name } } } = account;
								
									let currentSubscription = await getSubscription(id);
									
									if (!activeStatuses.includes(currentSubscription.status)) {
										return resolve({ error: 'Subscrption is not active' })
									}

									let pagarmePlan = await createPlan({
										...plan,
										charges: null,
										trial_days: null,
										invoice_reminder: null,
									});
									
									let checkout = {
										split_rules: null,
										reference_key: `${jobId}`,
										postback_url: CONSTANTS.PAGARME_SUBSCRIPTION_POSTBACK_URL,
										plan_id: pagarmePlan.id,
										payment_method,
										customer,
										billing: {
											name,
											address
										},
										metadata: {
											...currentSubscription.metadata,
											previousSubscriptions: [...new Set([ 
												...((currentSubscription.metadata||{}).previousSubscriptions||[]),
												id 
											])],
											previousSubscription: id,
											commission: 0,
											renewal: true
										},
									}
									
									if ( payment_method===CONSTANTS.PAGARME_GATEWAY_CREDIT_CARD && (card||{}).id ) {
										checkout.card_id = card.id;
									}
						
									let hasImplementation = Boolean(currentSubscription.metadata.implementation_id)
									let isPaid = true;
									
									if ( hasImplementation ) {
										try {
											let impTransaction = await getTransaction(currentSubscription.metadata.implementation_id);
											if (impTransaction && impTransaction.id) {
												isPaid = isPaid && transactionsPaidStatuses.includes(impTransaction.status)
											}
										} catch (error) {
											console.error('billingCron > getTransaction > error', { 
												error, 
												hasImplementation, 
												implementation_id: currentSubscription.metadata.implementation_id 
											});
										}
									}

									let newSubscription = await createSubscription(checkout);
									let { current_transaction } = newSubscription;

									isPaid = isPaid && transactionsPaidStatuses.includes((current_transaction||{}).status);
									
									const updatedAccount = {
										active: isPaid && activeStatuses.includes(newSubscription.status),
										modified: momentNow().format(CONSTANTS.MOMENT_ISO),
										pagarme: {
										   ...account.pagarme,
										   plan_id: pagarmePlan.id,
										   subscription_id: newSubscription.id,
										   subscription: newSubscription,
										   status: newSubscription.status,
										   payment_method: current_transaction.payment_method,
										   installments: current_transaction.installments,
										   days: pagarmePlan.days,
										}      
									};
									 
									if ( current_transaction.id && current_transaction.status==="paid" ) {
										await createTransactionInvoice({ transaction: current_transaction, account, description: `${langMessages["payment.subscription"]} ${pagarmePlan.name}` });
									}else{									
										let scheduled_moment = momentNow().set({ hour:0, minute:0, second:0, millisecond:0 }).add(7,'days')
										let scheduled_date = scheduled_moment.format(MOMENT_ISO)

										await addNewPost(COLLECTIONS.CRONJOBS_COLLECTION_NAME, {
											api: CRONJOB_SUBJECTS.INVOICE,
											executed: false,
											execution_date: "",
											scheduled_date,
											date: momentNow().format(MOMENT_ISO),
											modified: momentNow().format(MOMENT_ISO),
											type: CRONJOB_TYPES.BILLING_SCHEDULE,
											data: { 
												description: `${langMessages["payment.subscription"]} ${pagarmePlan.name}`,
												transaction_id: current_transaction.id,
												accountId, 
												ownerId: updatedAccount[CONSTANTS.OWNER_FIELD]
											},
											context,
										})
									}

									let scheduled_moment = momentNow().set({ hour:0, minute:0, second:0, millisecond:0 }).add(pagarmePlan.days,'days')
									let scheduled_date = scheduled_moment.format(MOMENT_ISO)

									await addNewPost(COLLECTIONS.CRONJOBS_COLLECTION_NAME, {
										api: CRONJOB_SUBJECTS.INVOICE,
										executed: false,
										execution_date: "",
										scheduled_date,
										date: momentNow().format(MOMENT_ISO),
										modified: momentNow().format(MOMENT_ISO),
										type: CRONJOB_TYPES.BILLING_SCHEDULE,
										data: { 
											description: `${langMessages["payment.subscription"]} ${pagarmePlan.name}`,
											subscription_id: newSubscription.id,
											accountId, 
											ownerId: updatedAccount[CONSTANTS.OWNER_FIELD]
										},
										context,
									})
									
									let accountResult = await accountRef.update(updatedAccount);
									let cronResult = await doc.ref.update({ results: { subscription: newSubscription, plan: pagarmePlan } });
									
									return resolve({ subscription: newSubscription, plan: pagarmePlan })

								} catch (error) {
									return resolve({ error })
								}
							}))
						break;
						default:break;
					}
					doc.ref.update({ executed: true, execution_date: momentNow().format(CONSTANTS.MOMENT_ISO) })
				})
				
				return res({ promises })

			}).catch(err=>console.error(err)||rej(err))
        });
	}
	
    return fetchCronjobs()
    .then(({ promises })=>{
		return Promise.all(promises)
	})
    .then(({ promises, err })=>{
        console.log('billingCron > results',{ err, promises: (promises && promises.length) });
        if (promises && promises.length) {
            return Promise.all(promises)
        }
        return null
    })
    .catch(error=>{
        console.error(error);
        return null;
    })
}

module.exports = {
	pagarmeApi,
	pagarmeClient,
	getPlan,
	getTransaction,
	getSubscription,
	getSubscriptionTransactions,
	createPlan,
	createSubscription,
	updateSubscription,
	createTransaction,
	cancelSubscription,
	createTransactionInvoice,
	billingCron
}