/**
 * App Constants
 *
 * @note
 * This file should be synced between the qiplus-frontend and the qiplus-firebase repos.
 *
 * Before making any changes to this file, please make sure the workspace.js script is
 * running on your environment.
 *
 * qiplus-frontend/src/constants/AppConstants.js > qiplus-firebase/functions/constants/index.js
 */
const APP_ID = "qiplus-50eee";
const DEV_APP_ID = "br-com-qiplus";

const SITE_HOSTNAME = "qiplus.com.br";
const DEV_HOSTNAME = "dev.qiplus.com.br";
const LOCAL_HOSTNAME = "localhost:3000";

const SITE_URL = `https://${SITE_HOSTNAME}`;
const DEV_URL = `https://${DEV_HOSTNAME}`;
const LOCAL_URL = `https://${LOCAL_HOSTNAME}`;

const APP_URL = `${SITE_URL}/app/`;
const DEV_APP_URL = `${DEV_URL}/app/`;
const LOCAL_APP_URL = `${LOCAL_URL}/app/`;

const CHAT_APP_HOSTNAME = "whatsapp.qi.plus"; // chat.qi.plus
const CHAT_APP_WEBSOCKET = "websocket.qi.plus"; // chat.qi.plus
const CHAT_APP_LOCAL_HOST = "localhost";
// TODO: Alterar para utilizar o chat api e o codechat local (-true funciona remoto, false funciona local)
const CHAT_APP_USE_REMOTE = true;
const CHAT_APP_PROTOCOL = CHAT_APP_USE_REMOTE ? "https" : "http";
const CHAT_APP_PORT = CHAT_APP_USE_REMOTE ? 443 : 8000;
const CHAT_APP_WEBSOCKET_PORT = CHAT_APP_USE_REMOTE ? 443 : 9000;
const CHAT_APP_URL = `${CHAT_APP_PROTOCOL}://${CHAT_APP_USE_REMOTE ? CHAT_APP_HOSTNAME : CHAT_APP_LOCAL_HOST
  }:${CHAT_APP_PORT}`;
const CHAT_APP_WEBSOCKET_URL = `${CHAT_APP_PROTOCOL}://${CHAT_APP_USE_REMOTE ? CHAT_APP_WEBSOCKET : CHAT_APP_LOCAL_HOST
  }:${CHAT_APP_WEBSOCKET_PORT}`;

const DNS_NS1_ADDRESS = "ns1.digitalocean.com";
const DNS_NS2_ADDRESS = "ns2.digitalocean.com";
const SERVER_IP_ADDRESS = "**************";

const PRODUCTION_ENV = "production";
const DEVELOPMENT_ENV = "development";
const PROD_ENV = PRODUCTION_ENV;
const DEV_ENV = DEVELOPMENT_ENV;

const IS_LOCAL_DEV =
  typeof window === "object" &&
  (window.location || {}).hostname === "localhost";
const IS_REMOTE_DEV =
  typeof window === "object" &&
  (window.location || {}).hostname === DEV_HOSTNAME;
const STORED_ENV =
  typeof localStorage === "object" && localStorage.getItem("env");
const FIREBASE_CONFIG =
  typeof process === "object" && (process.env || {}).FIREBASE_CONFIG
    ? JSON.parse(process.env.FIREBASE_CONFIG)
    : {};

const FIREBASE_ENV =
  FIREBASE_CONFIG.projectId === DEV_APP_ID
    ? DEV_ENV
    : FIREBASE_CONFIG.projectId === APP_ID
      ? PROD_ENV
      : null;
const NODE_ENV = FIREBASE_ENV || process.env.NODE_ENV;
const APP_ENV = STORED_ENV || (IS_REMOTE_DEV ? DEV_ENV : NODE_ENV);
const APP_ENVIROINMENT = APP_ENV;

const ENV_URL = IS_LOCAL_DEV ? LOCAL_URL : IS_REMOTE_DEV ? DEV_URL : SITE_URL;
const ENV_APP_URL = `${ENV_URL}/app/`;
const REMOTE_URL = APP_ENV === DEV_ENV ? DEV_URL : SITE_URL;
const REMOTE_APP_URL = `${REMOTE_URL}/app/`;
const TRACK_URL = `${REMOTE_URL}/track/`;
const LINK_TRACK_URL = `${TRACK_URL}r/`;
const PIXEL_TRACK_URL = `${TRACK_URL}p/`;
const BANNER_TRACK_URL = `${TRACK_URL}b/`;
const VIDEO_TRACK_URL = `${TRACK_URL}v/`;
const ALL_TRACK_URL_PARAMS = [
  LINK_TRACK_URL,
  PIXEL_TRACK_URL,
  BANNER_TRACK_URL,
  VIDEO_TRACK_URL,
];
const SUBSCRIPTION_URL = `${REMOTE_URL}/subscriptions/`;
const TUTORIALS_URL = `${SITE_URL}/tutorial/`;

const CHAT_APP_ENDPOINTS = {
  websocket: CHAT_APP_WEBSOCKET_URL,
  contacts: `${CHAT_APP_URL}/contacts`,
  chat: `${CHAT_APP_URL}/chat`,
  sendMsg: `${CHAT_APP_URL}/sendMsg`,
};

const DEFAULT_OWNER = "1";
const ORPHANS_OWNER = "-1";
const NO_OWNER = "-2";
const ORPHANS_ACCOUNT = "-1";
const NO_ACCOUNT = "-2";

const DEFAULT_LANG = "pt";
const DEFAULT_LOCALE = "pt_BR";
const MOMENT_ISO = "YYYY-MM-DD HH:mm:ss";
const MOMENT_ISO_MINUTES = "YYYY-MM-DD HH:mm";
const MOMENT_ISO_HOURS = "YYYY-MM-DD HH";
const MOMENT_ISO_DAY = "YYYY-MM-DD";
const MOMENT_ISO_WEEK = "YYYY-W";
const MOMENT_ISO_MONTH = "YYYY-MM";
const MOMENT_ISO_YEAR = "YYYY";
const MOMENT_LOCAL = "DD-MM-YYYY HH:mm:ss";
const MOMENT_SHORT = "DD/MM/YY HH:mm";
const MOMENT_SHORT_DAY = "DD/MM/YY";
const MOMENT_SHORT_MONTH = "MMM";
const MOMENT_DAY_MONTH = "DD/MM";
const MOMENT_RFC2822 = "ddd, DD MMM YYYY HH:mm:ss ZZ";
const MOMENT_DEBUG = "HH:mm:ss SSS - x";
const MOMENT_BR_OFFSET = -180;

const QIPLUS_TRACK_MARKUP_START = 'data-track="true" href="';
const QIPLUS_TRACK_MARKUP_END = '" data-qiplus="true"';
const QIPLUS_PIXEL_MARKUP_START =
  '<img id="qiplus-pixel" width="1" height="1" src="';
const QIPLUS_PIXEL_MARKUP_END = '/pixel.png">';
const QIPLUS_SUBSCRIPTION_MARKUP_START = `<a target="_blank" href="${SUBSCRIPTION_URL}?1=1`;
const QIPLUS_SUBSCRIPTION_MARKUP_END = '">Cancelar a inscrição</a>';

const QIPLUS_CLOUD_DOMAIN = "qiplus.cloud";
const EMAIL_APP_DOMAIN = "qiplus.cloud";
const MAILING_DOMAIN = "mail.qiplus.com.br";
const DEV_MAILING_DOMAIN = "devmail.qiplus.com.br";
const DEFAULT_MAILING_DOMAIN =
  APP_ENV === DEV_ENV ? DEV_MAILING_DOMAIN : MAILING_DOMAIN;
const DEFAULT_FROM_EMAIL = `mailing@${DEFAULT_MAILING_DOMAIN}`;
const WEBMASTER_ID = "1";
const WEBMASTER_EMAIL = "<EMAIL>";
const WEBMASTER_ACCOUNT_ID =
  APP_ENV === DEV_ENV
    ? "i9TtRqSINnaVr0f7x4uDBtk6SmY2"
    : "WYtM0UIKMXYVD6woul4zVMg2sr13";

const CHAT_KEY = "78e9032a-cef5-4aaf-9459-da7a3d66decc";

const qiplusEmailDomains = [
  EMAIL_APP_DOMAIN,
  MAILING_DOMAIN,
  DEV_MAILING_DOMAIN,
];

const QIPLUS_APP_ORIGIN = "QIPLUS_APP";
const WP_AJAX_URL = `${REMOTE_URL}/wp-admin/admin-ajax.php`;
const WP_EDIT_POST_URL = `${REMOTE_URL}/wp-admin/post.php?enviroinment=${APP_ENVIROINMENT}&action=edit&post=`;
const SEND_SMTP_MAIL_ACTION = "fck_send_smtp_mail";
const AJAX_ACTION_CREATE_PLAN = "fck_create_plan";
const AJAX_ACTION_UPDATE_PLAN = "fck_update_plan";
const AJAX_ACTION_CREATE_SUBSCRIPTION = "fck_create_subscription";
const AJAX_ACTION_UPDATE_SUBSCRIPTION = "fck_update_subscription";

const CRONJOB_TYPES = {
  DESKTOP_NOTIFICATION: "desktop-notification",
  AUTOMATION: "automation",
  BILLING_SCHEDULE: "BILLING_SCHEDULE",
  BATCH_EMAIL: "BATCH_EMAIL",
};

const CRONJOB_SUBJECTS = {
  TRANSACTION: "transaction",
  RENEWAL: "renewal",
  INVOICE: "invoice",
};

const STARTER_PLAN_ID = APP_ENV === DEV_ENV ? "11804" : "11804";
const PRO_PLAN_ID = APP_ENV === DEV_ENV ? "11816" : "11816";
const CORTEX_PLAN_ID = APP_ENV === DEV_ENV ? "11817" : "11817";
const SINAPSES_PLAN_ID = APP_ENV === DEV_ENV ? "11815" : "11815";

const STARTER_PLAN_SLUG = "starter";
const PRO_PLAN_SLUG = "pro";
const CORTEX_PLAN_SLUG = "cortex";
const SINAPSES_PLAN_SLUG = "sinapses";

const STARTER_PLAN_URL = `${APP_URL}plans/${STARTER_PLAN_SLUG}`;
const PRO_PLAN_URL = `${APP_URL}plans/${PRO_PLAN_SLUG}`;
const CORTEX_PLAN_URL = `${APP_URL}plans/${CORTEX_PLAN_SLUG}`;
const SINAPSES_PLAN_URL = `${APP_URL}plans/${SINAPSES_PLAN_SLUG}`;

const qiplusPlansRoutes = {
  [STARTER_PLAN_ID]: {
    route: `/plans/${STARTER_PLAN_SLUG}`,
    slug: STARTER_PLAN_SLUG,
    url: STARTER_PLAN_URL,
    envUrl: STARTER_PLAN_URL.replace(APP_URL, ENV_URL),
  },
  [PRO_PLAN_ID]: {
    route: `/plans/${PRO_PLAN_SLUG}`,
    slug: PRO_PLAN_SLUG,
    url: PRO_PLAN_URL,
    envUrl: PRO_PLAN_URL.replace(APP_URL, ENV_URL),
  },
  [CORTEX_PLAN_ID]: {
    route: `/plans/${CORTEX_PLAN_SLUG}`,
    slug: CORTEX_PLAN_SLUG,
    url: CORTEX_PLAN_URL,
    envUrl: CORTEX_PLAN_URL.replace(APP_URL, ENV_URL),
  },
  [SINAPSES_PLAN_ID]: {
    route: `/plans/${SINAPSES_PLAN_SLUG}`,
    slug: SINAPSES_PLAN_SLUG,
    url: SINAPSES_PLAN_URL,
    envUrl: SINAPSES_PLAN_URL.replace(APP_URL, ENV_URL),
  },
};

const GTM_ID = "GTM-MNS56H3";

const WEBHOOKS_URL = `${REMOTE_URL}/webhooks`;
const WOOCOMMERCE_WEBHOOK_URL = `${WEBHOOKS_URL}/woocommerce/?accountId=`;
const HOTMART_WEBHOOK_URL = `${WEBHOOKS_URL}/hotmart/?accountId=`;
const SHOPIFY_WEBHOOK_URL = `${WEBHOOKS_URL}/shopify/?accountId=`;
const EDUZZ_WEBHOOK_URL = `${WEBHOOKS_URL}/eduzz/?accountId=`;
const FORM_WEBHOOK_URL = `${WEBHOOKS_URL}/form/?accountId=`;
const FACEBOOK_LEADS_WEBHOOK_URL = `${WEBHOOKS_URL}/facebookleads/?accountId=`;

// NOTAZZ
const NOTAZZ_NFSE_POSTBACK_URL = `${SITE_URL}/?hook=notazz&subject=nfse&action=created&enviroinment=${APP_ENVIROINMENT}`;
const NOTAZZ_NFE_POSTBACK_URL = `${SITE_URL}/?hook=notazz&subject=nfe&action=created&enviroinment=${APP_ENVIROINMENT}`;
const NOTAZZ_API_KEY_PROD_MODE =
  "AZyATO5MzNzQTMkF2MzEmMjNTNjVTMjZTO1gjNldzNkpHd14ke5QVMudTMwMDMlhTMykjYiJGZ1QmN4QTMxQmYiljZ4AjMyMGZnZ";
const NOTAZZ_API_KEY_DEV_MODE =
  "wYyYDO0EzYjlDOmdjNwYmNzYjZyMGNklDZ4IzNlFDOjpHd14ke5QVMuNmM2gDNxM2Y5gjZ3YDMmZzM2YmMjRDZ5QGOycTZxgzYnZNGs7PJn5yWVmRLtzFsbw0gTYGau20gpYYVuDFgs3xxHyEoq2v5a5cc9VC5FCJy1hEbIvqiFmo9Uh6UH9";
const NOTAZZ_QIPLUS_API_KEY =
  APP_ENV === DEV_ENV ? NOTAZZ_API_KEY_DEV_MODE : NOTAZZ_API_KEY_PROD_MODE;
const AJAX_ACTION_CREATE_NOTAZZ_NFSE = "fck_create_nfse";
const AJAX_ACTION_CREATE_NOTAZZ_NFE = "fck_create_nfe";
const AJAX_ACTION_GET_NOTAZZ_NFSE = "fck_get_nfse";
const AJAX_ACTION_GET_NOTAZZ_NFE = "fck_get_nfe";

// PAGARME
const PAGARME_ENCRYPTION_KEY =
  APP_ENV === DEV_ENV
    ? "ek_test_EUETAbO0mPQmo2A0eqaFW34cQGYF4c"
    : "ek_live_01X4Qnn0hGs0LKRJcvew2W893X7Oze";
const PAGARME_SUBSCRIPTION_POSTBACK_URL = `${SITE_URL}/?hook=pagarme&subject=subscription&action=update&enviroinment=${APP_ENVIROINMENT}`;
const PAGARME_TRANSACTION_POSTBACK_URL = `${SITE_URL}/?hook=pagarme&subject=transaction&action=update&enviroinment=${APP_ENVIROINMENT}`;
const PAGARME_IMPLEMENTATION_POSTBACK_URL = `${SITE_URL}/?hook=pagarme&subject=implementation&action=update&enviroinment=${APP_ENVIROINMENT}`;
const PAGARME_PARCELA_POSTBACK_URL = `${SITE_URL}/?hook=pagarme&subject=parcela&action=update&enviroinment=${APP_ENVIROINMENT}`;
const PAGARME_UPGRADE_POSTBACK_URL = `${SITE_URL}/?hook=pagarme&subject=upgrade&action=update&enviroinment=${APP_ENVIROINMENT}`;
const PAGARME_RECIPIENT_POSTBACK_URL = `${SITE_URL}/?hook=pagarme&subject=recipient&action=update&enviroinment=${APP_ENVIROINMENT}`;
const PAGARME_API_PLANS_URL = `${SITE_URL}/?hook=pagarme&action=get&plans=true&enviroinment=${APP_ENVIROINMENT}`;
const PAGARME_API_PLANS_PARAMS =
  "&amount=&days=&trial_days=&installments=&payment_methods=&name=";

const PAGARME_GATEWAY_BOLETO = "boleto";
const PAGARME_GATEWAY_CREDIT_CARD = "credit_card";
const PAGARME_GATEWAY_CURRENT_CARD = "current_credit_card";

const PAGARME_PAID_STATUS = "paid";
const PAGARME_TRIALING_STATUS = "trialing";
const PAGARME_PENDING_PAYMENT_STATUS = "pending_payment";
const PAGARME_UNPAID_STATUS = "unpaid";
const PAGARME_CANCELED_STATUS = "canceled";
const PAGARME_ENDED_STATUS = "ended";

const PAGARME_SUBSCRIPTION_STATUSES = [
  PAGARME_PAID_STATUS,
  PAGARME_TRIALING_STATUS,
  PAGARME_PENDING_PAYMENT_STATUS,
  PAGARME_UNPAID_STATUS,
  PAGARME_CANCELED_STATUS,
  PAGARME_ENDED_STATUS,
];

const PAGARME_TRANSACTIONS_STATUSES = [
  "processing",
  "authorized",
  "paid",
  "refunded",
  "waiting_payment",
  "pending_refund",
  "refused",
];
const PAGARME_RECIPIENT_QIPLUS = {
  recipient_id:
    APP_ENV === DEV_ENV
      ? "re_ckh42rn5m01u40g9t7pwowfff"
      : "re_cjza6z0na4xdxyb5zom69ej1h",
  liable: true,
  charge_processing_fee: true,
  percentage: 100,
};

// BITLY
const BITLY_TOKEN = "****************************************";

// EMAIL APP
const IMAP_OPTION = "imap";
const GMAIL_APP_DOMAIN = "gmail.com";
// const GOOGLE_APIS_CLIENT_ID =
//   APP_ENV === DEV_ENV
//     ? '481494948477-aklgb7kttqthi85kdimjogcjbouqojf6.apps.googleusercontent.com'
//     : '728758974784-hdsc4okv43diihhcsbg2bsgs5hs09m9g.apps.googleusercontent.com'
const GOOGLE_APIS_CLIENT_ID =
  "728758974784-hdsc4okv43diihhcsbg2bsgs5hs09m9g.apps.googleusercontent.com";

const GOOGLE_APIS_CLIENT_SRC = "https://apis.google.com/js/client.js";
const GOOGLE_APIS_PLATFORM_SRC = "https://apis.google.com/js/platform.js";
// Array of API discovery doc URLs for APIs used by the quickstart
const GMAIL_APP_DISCOVERY_DOCS = [
  "https://www.googleapis.com/discovery/v1/apis/gmail/v1/rest",
];
// Authorization scopes required by the API; multiple scopes can be included, separated by spaces.
const GMAIL_APP_SCOPES = "https://mail.google.com/";
const BASE64_NPM_SCR =
  "https://cdn.jsdelivr.net/npm/js-base64@3.4.5/base64.min.js";
// Client Secret
// UCz7C4M-QSeDwPNmSSE_cdoO

// ADWORDS
const GOOGLE_ADS_CREDENTIALS = {
  api_key: process.env.GOOGLE_ADS_API_KEY || "YOUR_API_KEY_HERE",
  developer_token: process.env.GOOGLE_ADS_DEVELOPER_TOKEN || "YOUR_TOKEN_HERE",
  client_id: process.env.GOOGLE_ADS_CLIENT_ID || "YOUR_CLIENT_ID_HERE",
  client_secret: process.env.GOOGLE_ADS_CLIENT_SECRET || "YOUR_SECRET_HERE",
  scope:
    process.env.GOOGLE_ADS_SCOPE || "https://www.googleapis.com/auth/adwords",
  tutorialUrlVideo: process.env.GOOGLE_ADS_TUTORIAL_URL_VIDEO,
};

const HOTMAIL_APP_DOMAIN = "hotmail.com";
const LIVE_APP_DOMAIN = "live.com";
const OUTLOOK_APP_DOMAIN = "outlook.com";
const MSN_APP_DOMAIN = "msn.com";
const MSA_APP_DOMAINS = [
  HOTMAIL_APP_DOMAIN,
  LIVE_APP_DOMAIN,
  OUTLOOK_APP_DOMAIN,
  MSN_APP_DOMAIN,
];
const MSA_API_CLIENT_ID =
  APP_ENV === DEV_ENV
    ? "5227437d-86c3-48c8-856e-cc6bb08311d9"
    : "5227437d-86c3-48c8-856e-cc6bb08311d9";
const MSA_API_REDIRECT_URI =
  APP_ENV === DEV_ENV
    ? `http://${LOCAL_HOSTNAME}/app/mail`
    : `${APP_URL}/mail`.replace("//mail", "/mail");
const MSA_API_TOKEN_SRC =
  "https://login.microsoftonline.com/common/oauth2/v2.0/token";
const MSA_API_AUTHORIZE_SRC = `https://login.microsoftonline.com/common/oauth2/v2.0/authorize?client_id=${MSA_API_CLIENT_ID}&response_type=code&redirect_uri=${MSA_API_REDIRECT_URI}&response_mode=query&scope=openid%20offline_access%20https%3A%2F%2Fgraph.microsoft.com%2Femail%20https%3A%2F%2Fgraph.microsoft.com%2Fmail.read%20https%3A%2F%2Fgraph.microsoft.com%2Fmail.readwrite%20https%3A%2F%2Fgraph.microsoft.com%2Fmail.send%20https%3A%2F%2Fgraph.microsoft.com%2Fuser.read&state=123454321&prompt=consent`;
const MSA_GRAPH_API_SRC = "https://graph.microsoft.com/v1.0/me/";

const FUNCTIONS_EXECUTION_LIMIT = 100000;
const FIRESTORE_MAX_LIMIT = 10000;
const FIRESTORE_MAX_DOCUMENT_SIZE = 1048576;
const FIRESTORE_SAFE_DOCUMENT_FIELD_SIZE = FIRESTORE_MAX_DOCUMENT_SIZE * 0.75;

const YEARLY = "yearly";
const MONTHLY = "monthly";

const timer_1_Second = 1000;
const timer_2_Seconds = 2000;
const timer_3_Seconds = 3000;
const timer_4_Seconds = 4000;
const timer_5_Seconds = 5000;
const timer_10_Seconds = 10 * 1000;
const timer_30_Seconds = 30 * 1000;
const timer_1_Minute = 60 * 1000;
const timer_5_Minutes = 5 * 60 * 1000;
const timer_10_Minutes = 10 * 60 * 1000;
const timer_30_Minutes = 30 * 60 * 1000;
const timer_1_Hour = 60 * 60 * 1000;

const QIPLUS_STORE_ID = "3823";

const authPaths = ["/login", "/register", "/forgot-password"];
const frontendPaths = [...authPaths, "/plans", "/player"];

// url params
const URL_PARAMS_TAG_ID = "tagId";
const URL_PARAMS_PARENT_ID = "parentId";
const URL_PARAMS_AFFILIATE_ID = "affiliateId";
const URL_PARAMS_DOC_ID = "docId";
const URL_PARAMS_ACCOUNT_ID = "accountId";
const URL_PARAMS_COLLECTION = "collection";

const SHORT_URL_PARAMS = {
  [URL_PARAMS_DOC_ID]: "d",
  [URL_PARAMS_ACCOUNT_ID]: "a",
  [URL_PARAMS_COLLECTION]: "c",
};

const DOC_DOESNT_EXIST = "DOC_DOESNT_EXIST";

// indexes
const CREATED_FIELD = "createdAt";
const UPDATED_FIELD = "updatedAt";

// statuses
const PUBLISH_STATUS = "publish";
const DRAFT_STATUS = "draft";
const TRASH_STATUS = "trash";
const ACTIVE_STATUS = "active";
const INACTIVE_STATUS = "inactive";
const DELETED_STATUS = "deleted";

// leads
const NAMES_FIELDS_GROUP = "names";
const CONTACT_FIELDS_GROUP = "contact";
const PROFESSIONAL_FIELDS_GROUP = "professional";
const OTHER_FIELDS_GROUP = "other";
const ADDRESS_FIELDS_GROUP = "address";
const QIUSERS_FIELDS_GROUP = "qiusers";
const SOCIALNEWTORKS_FIELDS_GROUP = "socialNewtorks";
const QI_FIELDS_GROUP = "qiFields";

// other
const DATE_FIELDS_GROUP = "dateFields";
const CONTRACT_FIELDS_GROUP = "contracts";
const TICKET_FIELDS_GROUP = "tickets";
const CUSTOM_FIELDS_GROUP = "customFields";
const TEAM_FIELDS_GROUP = "team";

// path
const QIUSERS_FIELDS_PATH = "qiuser";
const ADDRESS_FIELDS_PATH = "address";
const CUSTOM_FIELDS_PATH = "customFields";
const CONTRACT_FIELDS_PATH = "contracts";
const TICKET_FIELDS_PATH = "tickets";

const CONTRACT_NUMBER_FIELD = "data:contractNumber";
const CONTRACT_MODIFIED_FIELD = "modified";
const CONTRACT_DATE_FIELD = "date";

const TICKET_NUMBER_FIELD = "ID";
const TICKET_ITEMS_FIELD = "items";
const TICKET_GATEWAY_FIELD = "payment:gateway";
const TICKET_TOTAL_FIELD = "data:total";
const TICKET_MODIFIED_FIELD = "modified";
const TICKET_DATE_FIELD = "date";

const AVATAR_FIELD = "avatar";
const QRCODE_FIELD = "qrcode";

const ID_FIELD = "ID";
const LOG_ID_FIELD = "logId";
const LOG_RELATED_ID_FIELD = "id";

const SCORE_FIELD = "score";
const TYPE_FIELD = "type";
const DATE_FIELD = "date";
const MODIFIED_FIELD = "modified";
const DATEREGISTERED_FIELD = "date";

const DESCRIPTION_FIELD = "description";
const MARITAL_STATUS_FIELD = "maritalStatus";
const GENDER_FIELD = "gender";

const SENT_PLUSDATE_FIELD = "sentPlusDate";
const SENT_FULLDATE_FIELD = "sentFullDate";
const SENT_DATE_FIELD = "sentDate";
const SENT_DAY_FIELD = "sentDay";
const SENT_MONTH_FIELD = "sentMonth";
const SENT_YEAR_FIELD = "sentYear";

const OPERATOR_ID_FIELD = "operator_id";

const SELLER_FIELD = "seller";
const SELLERNAME_FIELD = "seller:displayName";
const SELLEREMAIL_FIELD = "seller:email";
const SELLERMOBILE_FIELD = "seller:mobile";
const SELLERPHONE_FIELD = "seller:phone";

const PROMOTER_FIELD = "promoter";
const PROMOTERNAME_FIELD = "promoter:displayName";
const PROMOTEREMAIL_FIELD = "promoter:email";
const PROMOTERMOBILE_FIELD = "promoter:mobile";
const PROMOTERPHONE_FIELD = "promoter:phone";

const MANAGER_FIELD = "manager";
const MANAGERNAME_FIELD = "manager:displayName";
const MANAGEREMAIL_FIELD = "manager:email";
const MANAGERMOBILE_FIELD = "manager:mobile";
const MANAGERPHONE_FIELD = "manager:phone";

const ROLES_FIELD = "roles";
const LEVEL_FIELD = "level";

const VALID_EMAIL_FIELD = "is_valid";
const EMAIL_FIELD = "email";
const CONTACT_FIELD = "contact";
const QIUSER_FIELD = "qiuser";
const AUTHOR_FIELD = "author";
const OWNER_FIELD = "owner";
const TEAM_FIELD = "team";
const AFFILIATE_FIELD = "affiliateId";
const PARENT_FIELD = "parentId";
const ACCOUNT_FIELD = "accountId";
const AUTOMATION_FIELD = "automationId";
const CAMPAIGN_FIELD = "campaignId";
const LANDING_PAGE_FIELD = "landingPageId";
const CONTACT_ID_FIELD = "contactId";
const FUNNEL_ID_FIELD = "funnel";
const STORES_FIELD = "stores";

const RELATIONAL_USERS_FIELDS = [
  OPERATOR_ID_FIELD,
  SELLER_FIELD,
  PROMOTER_FIELD,
  MANAGER_FIELD,
  QIUSER_FIELD,
  AUTHOR_FIELD,
  OWNER_FIELD,
  TEAM_FIELD,
  AFFILIATE_FIELD,
];

const RELATIONAL_FIELDS = [
  ...RELATIONAL_USERS_FIELDS,
  PARENT_FIELD,
  ACCOUNT_FIELD,
  CAMPAIGN_FIELD,
  LANDING_PAGE_FIELD,
  CONTACT_ID_FIELD,
  FUNNEL_ID_FIELD,
];

const RELATIONAL_USERS_COLLECTIONS = {
  [OPERATOR_ID_FIELD]: "qiusers",
  [SELLER_FIELD]: "qiusers",
  [PROMOTER_FIELD]: "qiusers",
  [MANAGER_FIELD]: "qiusers",
  [QIUSER_FIELD]: "qiusers",
  [AUTHOR_FIELD]: "qiusers",
  [OWNER_FIELD]: "qiusers",
  [TEAM_FIELD]: "teams",
  [AFFILIATE_FIELD]: "affiliates",
};

const RELATIONAL_COLLECTIONS = {
  ...RELATIONAL_USERS_COLLECTIONS,
  [PARENT_FIELD]: "accounts",
  [ACCOUNT_FIELD]: "accounts",
  [CAMPAIGN_FIELD]: "campaigns",
  [LANDING_PAGE_FIELD]: "landing-pages",
  [CONTACT_ID_FIELD]: "leads",
  [FUNNEL_ID_FIELD]: "funnels",
};

const TRACK_PARAM_REFERRER = "referrer";
const TRACK_PARAM_USER_AGENT = "user_agent";
const TRACK_PARAM_REMOTE_ADDR = "remote_addr";
const TRACK_PARAMS = [
  TRACK_PARAM_REFERRER,
  TRACK_PARAM_USER_AGENT,
  TRACK_PARAM_REMOTE_ADDR,
];

const COLLECTION_FIELD = "collection";
const KEYWORDS_FIELD = "keywords";
const KEYWORDS_FIELDKEY_FIELD = "field";

const LOCATION_FIELDS_MAP = ["city", "state", "country"];
const ADDRESS_FIELDS_MAP = [
  "postalCode",
  "street",
  "num",
  "comp",
  "ref",
  "neighborhood",
  "city",
  "state",
  "country",
];

// SHORTCODE
const SHORTCODE_PREFIX = "[::qi-";
const SHORTCODE_SUFIX = "::]";
const SHORTCODE_PATH_SEPARATOR = "/";
const SHORTCODE_LABEL_SEPARATOR = "==";
const SHORTCODE_PARAMS_SEPARATOR = "+";
const ADDRESS_SHORTCODES_FIELDS_MAP = ADDRESS_FIELDS_MAP.map(
  (f) => `${ADDRESS_FIELDS_GROUP}:${f}`
);

// FUNNEL MODE
const SINGLE_PRODUCT_MODE = "SINGLE_PRODUCT_MODE";
const CART_MODE = "CART_MODE";
const SINGLE_VALUE_MODE = "SINGLE_VALUE_MODE";
const VALUES_MODE = "VALUES_MODE";
const DEFAULT_SALES_MODE = VALUES_MODE;
const FIRST_STAGE = "0";
const WON_STAGE = "won";
const LOST_STAGE = "lost";

const FUNNEL_STAGES_CONSTANTS = {
  first: FIRST_STAGE,
  won: WON_STAGE,
  lost: LOST_STAGE,
};

// DISTRIBUTION MODE
const EQUAL_MODE = "equal";
const RANDOM_MODE = "random";

// APP TRIGGERS
const EMAIL_TRIGGERS = {
  sent: "sent",
  accepted: "accepted",
  complained: "complained",
  unsubscribed: "unsubscribed",
  rejected: "rejected",
  delivered: "delivered",
  opened: "opened",
  clicked: "clicked",
  failed: "failed",
};

const APP_TRIGGER_ADDED = "added";
const APP_TRIGGER_REMOVED = "removed";
const APP_TRIGGER_UPDATED = "updated";

const APP_TRIGGER_ADDED_PARTICIPANT = "added_as_participant";
const APP_TRIGGER_REMOVED_PARTICIPANT = "removed_from_participants";
const APP_TRIGGER_CONFIRMED_PARTICIPANT = "confirmed_participant";
const APP_TRIGGER_UNCONFIRMED_PARTICIPANT = "unconfirmed_participant";
const APP_TRIGGER_ADDED_VIA_INTEGRATION = "added_via_integration";
const APP_TRIGGER_UPDATED_VIA_INTEGRATION = "updated_via_integration";
const APP_TRIGGER_ADDED_TO_SEGMENTATION = "added_to_segmentation";
const APP_TRIGGER_REMOVED_FROM_SEGMENTATION = "removed_from_segmentation";
const APP_TRIGGER_ADDED_TO_STORE = "added_to_store";
const APP_TRIGGER_REMOVED_FROM_STORE = "removed_from_store";
const APP_TRIGGER_ADDED_TO_FUNNEL = "added_to_funnel";
const APP_TRIGGER_TICKET_CANCELED = "tickets_canceled";
const APP_TRIGGER_CHECKEDIN = "checkedIn";
const APP_TRIGGER_DIDNTCHECKIN = "didntCheckin";
const APP_TRIGGER_CANCELED = "canceled";
const APP_TRIGGER_BOUGHT = "bought";
const APP_TRIGGER_NEVER_BOUGHT = "neverBought";
const APP_TRIGGER_CONVERTED = "converted";
const APP_TRIGGER_NEVER_CONVERTED = "neverConverted";
const APP_TRIGGER_CURRENT_STAGE = "stage";
const APP_TRIGGER_STAGEIN = "stageIn";
const APP_TRIGGER_STAGEOUT = "stageOut";
const APP_TRIGGER_WON = "won";
const APP_TRIGGER_LOST = "lost";
const APP_TRIGGER_PROGRESSED = "progressed";
const APP_TRIGGER_REGRESSED = "regressed";
const APP_TRIGGER_FIRED = "fired";
const APP_TRIGGER_SUBMITED = "submited";

const APP_TRIGGER_SENT = EMAIL_TRIGGERS.sent;
const APP_TRIGGER_ACCEPTED = EMAIL_TRIGGERS.accepted;
const APP_TRIGGER_COMPLAINED = EMAIL_TRIGGERS.complained;
const APP_TRIGGER_UNSUBSCRIBED = EMAIL_TRIGGERS.unsubscribed;
const APP_TRIGGER_REJECTED = EMAIL_TRIGGERS.rejected;
const APP_TRIGGER_DELIVERED = EMAIL_TRIGGERS.delivered;
const APP_TRIGGER_OPENED = EMAIL_TRIGGERS.opened;
const APP_TRIGGER_CLICKED = EMAIL_TRIGGERS.clicked;
const APP_TRIGGER_FAILED = EMAIL_TRIGGERS.failed;
const APP_TRIGGER_DIDNT_OPEN = "didnt_open";
const APP_TRIGGER_DIDNT_CLICK = "didnt_click";

const APP_TRIGGER_TAG_ADDED = "tag_added";
const APP_TRIGGER_TAG_REMOVED = "tag_removed";
const APP_TRIGGER_TAGHASTAG = "tagHasTag";
const APP_TRIGGER_TAGHASNOT = "tagHasNot";

const APP_TRIGGER_COMPLETED = "completed";
const APP_TRIGGER_VISITED = "visited";
const APP_TRIGGER_VIEWED = "viewed";
const APP_TRIGGER_PLAYED = "played";
const APP_TRIGGER_UNLOAD = "unload";
const APP_TRIGGER_IMPRESSION = "impression";
const APP_TRIGGER_LEADS_ADDED = "leads_added";

const APP_TRIGGER_MESSAGE_RECEIVED = "message_received";
const APP_TRIGGER_INTERATION_ADDED = "interaction_added";
const APP_TRIGGER_INTERATION_REMOVED = "interaction_removed";

const APP_TRIGGER_SELLER_CHANGED = `${SELLER_FIELD}_changed`;
const APP_TRIGGER_PROMOTER_CHANGED = `${PROMOTER_FIELD}_changed`;
const APP_TRIGGER_MANAGER_CHANGED = `${MANAGER_FIELD}_changed`;
const APP_TRIGGER_QIUSER_CHANGED = `${QIUSER_FIELD}_changed`;
const APP_TRIGGER_AUTHOR_CHANGED = `${AUTHOR_FIELD}_changed`;
const APP_TRIGGER_OWNER_CHANGED = `${OWNER_FIELD}_changed`;
const APP_TRIGGER_TEAM_CHANGED = `${TEAM_FIELD}_changed`;
const APP_TRIGGER_AFFILIATE_CHANGED = `${AFFILIATE_FIELD}_changed`;
const APP_TRIGGER_PARENT_CHANGED = `${PARENT_FIELD}_changed`;
const APP_TRIGGER_ACCOUNT_CHANGED = `${ACCOUNT_FIELD}_changed`;
const APP_TRIGGER_CAMPAIGN_CHANGED = `${CAMPAIGN_FIELD}_changed`;
const APP_TRIGGER_CONTACT_CHANGED = `${CONTACT_ID_FIELD}_changed`;
const APP_TRIGGER_FUNNEL_CHANGED = `${FUNNEL_ID_FIELD}_changed`;
const APP_TRIGGER_SELLER_REMOVED = `${SELLER_FIELD}_removed`;
const APP_TRIGGER_PROMOTER_REMOVED = `${PROMOTER_FIELD}_removed`;
const APP_TRIGGER_MANAGER_REMOVED = `${MANAGER_FIELD}_removed`;
const APP_TRIGGER_QIUSER_REMOVED = `${QIUSER_FIELD}_removed`;
const APP_TRIGGER_AUTHOR_REMOVED = `${AUTHOR_FIELD}_removed`;
const APP_TRIGGER_OWNER_REMOVED = `${OWNER_FIELD}_removed`;
const APP_TRIGGER_TEAM_REMOVED = `${TEAM_FIELD}_removed`;
const APP_TRIGGER_AFFILIATE_REMOVED = `${AFFILIATE_FIELD}_removed`;
const APP_TRIGGER_PARENT_REMOVED = `${PARENT_FIELD}_removed`;
const APP_TRIGGER_ACCOUNT_REMOVED = `${ACCOUNT_FIELD}_removed`;
const APP_TRIGGER_CAMPAIGN_REMOVED = `${CAMPAIGN_FIELD}_removed`;
const APP_TRIGGER_CONTACT_REMOVED = `${CONTACT_ID_FIELD}_removed`;
const APP_TRIGGER_FUNNEL_REMOVED = `${FUNNEL_ID_FIELD}_removed`;

const APP_TRIGGER_COOKIES_POLICY_ACCEPTED = "cookies_policy_accepted";
const APP_TRIGGER_PRIVACY_POLICY_ACCEPTED = "privacy_policy_accepted";
const APP_TRIGGER_TERMS_OF_USE_ACCEPTED = "terms_of_use_accepted";

const APP_TRIGGERS = {
  APP_TRIGGER_ADDED,
  APP_TRIGGER_REMOVED,
  APP_TRIGGER_ADDED_PARTICIPANT,
  APP_TRIGGER_REMOVED_PARTICIPANT,
  APP_TRIGGER_CONFIRMED_PARTICIPANT,
  APP_TRIGGER_UNCONFIRMED_PARTICIPANT,
  APP_TRIGGER_ADDED_VIA_INTEGRATION,
  APP_TRIGGER_UPDATED_VIA_INTEGRATION,
  APP_TRIGGER_ADDED_TO_SEGMENTATION,
  APP_TRIGGER_REMOVED_FROM_SEGMENTATION,
  APP_TRIGGER_ADDED_TO_STORE,
  APP_TRIGGER_REMOVED_FROM_STORE,
  APP_TRIGGER_ADDED_TO_FUNNEL,
  APP_TRIGGER_TICKET_CANCELED,
  APP_TRIGGER_CHECKEDIN,
  APP_TRIGGER_DIDNTCHECKIN,
  APP_TRIGGER_CANCELED,
  APP_TRIGGER_BOUGHT,
  APP_TRIGGER_NEVER_BOUGHT,
  APP_TRIGGER_CONVERTED,
  APP_TRIGGER_NEVER_CONVERTED,
  APP_TRIGGER_CURRENT_STAGE,
  APP_TRIGGER_STAGEIN,
  APP_TRIGGER_STAGEOUT,
  APP_TRIGGER_WON,
  APP_TRIGGER_LOST,
  APP_TRIGGER_PROGRESSED,
  APP_TRIGGER_REGRESSED,
  APP_TRIGGER_FIRED,
  APP_TRIGGER_SUBMITED,
  APP_TRIGGER_SENT,
  APP_TRIGGER_ACCEPTED,
  APP_TRIGGER_COMPLAINED,
  APP_TRIGGER_UNSUBSCRIBED,
  APP_TRIGGER_REJECTED,
  APP_TRIGGER_DELIVERED,
  APP_TRIGGER_OPENED,
  APP_TRIGGER_CLICKED,
  APP_TRIGGER_FAILED,
  APP_TRIGGER_DIDNT_OPEN,
  APP_TRIGGER_DIDNT_CLICK,
  APP_TRIGGER_UPDATED,
  APP_TRIGGER_TAG_ADDED,
  APP_TRIGGER_TAG_REMOVED,
  APP_TRIGGER_TAGHASTAG,
  APP_TRIGGER_TAGHASNOT,
  APP_TRIGGER_COMPLETED,
  APP_TRIGGER_VISITED,
  APP_TRIGGER_VIEWED,
  APP_TRIGGER_PLAYED,
  APP_TRIGGER_UNLOAD,
  APP_TRIGGER_IMPRESSION,
  APP_TRIGGER_SELLER_CHANGED,
  APP_TRIGGER_PROMOTER_CHANGED,
  APP_TRIGGER_MANAGER_CHANGED,
  APP_TRIGGER_QIUSER_CHANGED,
  APP_TRIGGER_AUTHOR_CHANGED,
  APP_TRIGGER_OWNER_CHANGED,
  APP_TRIGGER_TEAM_CHANGED,
  APP_TRIGGER_AFFILIATE_CHANGED,
  APP_TRIGGER_PARENT_CHANGED,
  APP_TRIGGER_ACCOUNT_CHANGED,
  APP_TRIGGER_CAMPAIGN_CHANGED,
  APP_TRIGGER_CONTACT_CHANGED,
  APP_TRIGGER_FUNNEL_CHANGED,
  APP_TRIGGER_SELLER_REMOVED,
  APP_TRIGGER_PROMOTER_REMOVED,
  APP_TRIGGER_MANAGER_REMOVED,
  APP_TRIGGER_QIUSER_REMOVED,
  APP_TRIGGER_AUTHOR_REMOVED,
  APP_TRIGGER_OWNER_REMOVED,
  APP_TRIGGER_TEAM_REMOVED,
  APP_TRIGGER_AFFILIATE_REMOVED,
  APP_TRIGGER_PARENT_REMOVED,
  APP_TRIGGER_ACCOUNT_REMOVED,
  APP_TRIGGER_CAMPAIGN_REMOVED,
  APP_TRIGGER_CONTACT_REMOVED,
  APP_TRIGGER_FUNNEL_REMOVED,
  APP_TRIGGER_COOKIES_POLICY_ACCEPTED,
  APP_TRIGGER_PRIVACY_POLICY_ACCEPTED,
  APP_TRIGGER_TERMS_OF_USE_ACCEPTED,
  APP_TRIGGER_LEADS_ADDED,
  APP_TRIGGER_MESSAGE_RECEIVED,
  APP_TRIGGER_INTERATION_ADDED,
  APP_TRIGGER_INTERATION_REMOVED,
};

const CONDITIONAL_TRIGGERS = {
  APP_TRIGGER_DIDNTCHECKIN,
  APP_TRIGGER_NEVER_BOUGHT,
  APP_TRIGGER_NEVER_CONVERTED,
  APP_TRIGGER_DIDNT_OPEN,
  APP_TRIGGER_DIDNT_CLICK,
};

const RELATIONAL_TRIGGERS = {};

RELATIONAL_FIELDS.forEach((field) => {
  RELATIONAL_TRIGGERS[`${field}_changed`] = `${field}_changed`;
  RELATIONAL_TRIGGERS[`${field}_removed`] = `${field}_removed`;
  APP_TRIGGERS[
    `APP_TRIGGER_${field.toUpperCase()}_CHANGED`
  ] = `${field}_changed`;
  APP_TRIGGERS[
    `APP_TRIGGER_${field.toUpperCase()}_REMOVED`
  ] = `${field}_removed`;
});

const AppTriggersColors = {
  [APP_TRIGGER_SENT]: "warning",
  [APP_TRIGGER_ACCEPTED]: "warning",
  [APP_TRIGGER_DELIVERED]: "info",
  [APP_TRIGGER_OPENED]: "primary",
  [APP_TRIGGER_VIEWED]: "primary",
  [APP_TRIGGER_CLICKED]: "success",
  [APP_TRIGGER_FAILED]: "danger",
  [APP_TRIGGER_COMPLAINED]: "github",
  [APP_TRIGGER_UNSUBSCRIBED]: "github",
  [APP_TRIGGER_REJECTED]: "github",
};

// APP ACTIONS
const APP_ACTION_EMAIL = "ACTION_EMAIL";
const APP_ACTION_NOTIFICATE_SELLER = "ACTION_NOTIFICATE_SELLER";
const APP_ACTION_NOTIFICATE_MANAGER = "ACTION_NOTIFICATE_MANAGER";
const APP_ACTION_CONTRACT = "ACTION_CONTRACT";
const APP_ACTION_SEGMENTATIONS = "ACTION_SEGMENTATIONS";
const APP_ACTION_TEAMS = "ACTION_TEAMS";
const APP_ACTION_SET_SELLER = "ACTION_SET_SELLER";
const APP_ACTION_SET_MANAGER = "ACTION_SET_MANAGER";
const APP_ACTION_EVENT = "ACTION_EVENT";
const APP_ACTION_CAMPAIGN = "ACTION_CAMPAIGN";
const APP_ACTION_GO_TO_ACTION = "ACTION_GO_TO_ACTION";
const APP_ACTION_GO_TO_AUTOMATION = "ACTION_GO_TO_AUTOMATION";
const APP_ACTION_EVALUATE = "ACTION_EVALUATE";
const APP_ACTION_WEBHOOK = "ACTION_WEBHOOK";
const APP_ACTION_DEAL = "ACTION_DEAL";
const APP_ACTION_DEAL_UPDATE = "ACTION_DEAL_UPDATE";
const APP_ACTION_DEAL_UPDATE_IN_FUNNEL = "ACTION_DEAL_UPDATE_IN_FUNNEL";
const APP_ACTION_DEAL_WEBHOOK = "ACTION_DEAL_WEBHOOK";
const APP_ACTION_FUNNEL_DEAL = "ACTION_FUNNEL_DEAL";
const APP_ACTION_FUNNEL = "ACTION_FUNNEL";
const APP_ACTION_FUNNEL_SWITCHER = "ACTION_FUNNEL_SWITCHER";
const APP_ACTION_FUNNEL_PROGGRESS = "ACTION_FUNNEL_PROGGRESS";
const APP_ACTION_FUNNEL_REGGRESS = "ACTION_FUNNEL_REGGRESS";
const APP_ACTION_STORE = "ACTION_STORE";
const APP_ACTION_CREATE_TICKET = "ACTION_CREATE_TICKET";
const APP_ACTION_USER_TASKLIST = "ACTION_USER_TASKLIST";
const APP_ACTION_TEAM_TASKLIST = "ACTION_TEAM_TASKLIST";
const APP_ACTION_DEAL_TASKLIST = "ACTION_DEAL_TASKLIST";
const APP_ACTION_TAGS = "ACTION_TAGS";
const APP_ACTION_REMOVE_TAGS = "ACTION_REMOVE_TAGS";
const APP_ACTION_DEALS_TAGS = "ACTION_DEALS_TAGS";
const APP_ACTION_REMOVE_DEALS_TAGS = "ACTION_REMOVE_DEALS_TAGS";
const APP_ACTION_PROFILE = "ACTION_PROFILE";
const APP_ACTION_SCORE = "ACTION_SCORE";
const APP_ACTION_TIMER = "ACTION_TIMER";
const APP_ACTION_SCHEDULE = "ACTION_SCHEDULE";
const APP_ACTION_LOG = "ACTION_LOG";
const APP_ACTION_SHOTX_SEND_TEXT_MESSAGE = "ACTION_SHOTX_SEND_TEXT_MESSAGE";

const APP_ACTIONS = {
  APP_ACTION_EMAIL,
  APP_ACTION_NOTIFICATE_SELLER,
  APP_ACTION_NOTIFICATE_MANAGER,
  APP_ACTION_CONTRACT,
  APP_ACTION_SEGMENTATIONS,
  APP_ACTION_TEAMS,
  APP_ACTION_SET_SELLER,
  APP_ACTION_SET_MANAGER,
  APP_ACTION_EVENT,
  APP_ACTION_CAMPAIGN,
  APP_ACTION_GO_TO_ACTION,
  APP_ACTION_GO_TO_AUTOMATION,
  APP_ACTION_EVALUATE,
  APP_ACTION_WEBHOOK,
  APP_ACTION_DEAL,
  APP_ACTION_DEAL_UPDATE,
  APP_ACTION_DEAL_UPDATE_IN_FUNNEL,
  APP_ACTION_DEAL_WEBHOOK,
  APP_ACTION_FUNNEL_DEAL,
  APP_ACTION_FUNNEL,
  APP_ACTION_FUNNEL_SWITCHER,
  APP_ACTION_FUNNEL_PROGGRESS,
  APP_ACTION_FUNNEL_REGGRESS,
  APP_ACTION_STORE,
  APP_ACTION_CREATE_TICKET,
  APP_ACTION_USER_TASKLIST,
  APP_ACTION_TEAM_TASKLIST,
  APP_ACTION_DEAL_TASKLIST,
  APP_ACTION_TAGS,
  APP_ACTION_SHOTX_SEND_TEXT_MESSAGE,
  APP_ACTION_REMOVE_TAGS,
  APP_ACTION_DEALS_TAGS,
  APP_ACTION_REMOVE_DEALS_TAGS,
  APP_ACTION_PROFILE,
  APP_ACTION_SCORE,
  APP_ACTION_TIMER,
  APP_ACTION_SCHEDULE,
  APP_ACTION_LOG,
};

// SMTP
const SMTP_ENABLED = "smtp_enabled";
const MAILING_QIPLUS_DISABLED = "mailing_qiplus_disabled";

const OTHER_SMTP = "smtp";
const QIPLUS_SMTP = "qiplus";
const MAILBOXES_SMTP = "mailboxes";
const MAIL_DOMAIN = "mail_domain";

const QIPLUS_SMTP_OPTIONS = {
  [OTHER_SMTP]: "SMTP Próprio",
  [QIPLUS_SMTP]: "QIPlus",
  [MAILBOXES_SMTP]: "Caixa de Entrada",
  [MAIL_DOMAIN]: "Domínio Próprio",
};

// EMAIL SERVICES
const MAILGUN_SERVICE = "mailgun";
const RESEND_SERVICE = "resend";

const CHATAPI_LINK = `https://shotxv2dev.qi.plus`;

// INTEGRATIONS
const FORM_PLATFORM = "form";
const SMTP_PLATFORM = "smtp";
const MAIL_DOMAIN_PLATFORM = "maildomain";
const GMAIL_SMTP_PLATFORM = "gmail";
const RDSTATION_PLATFORM = "rdstation";
const MAUTIC_PLATFORM = "mautic";
const INFUSIONSOFT_PLATFORM = "infusionsoft";
const LEADLOVERS_PLATFORM = "leadlovers";
const FACEBOOK_CAPI_PLATFORM = "facebookcapi";
const FACEBOOK_LEADS_PLATFORM = "facebookleads";
const FACEBOOK_PAGES_PLATFORM = "facebookpages";
const FACEBOOK_ADS_PLATFORM = "facebookads";
const G_FORMS_PLATFORM = "googleforms";
const G_CALENDAR_PLATFORM = "g_calendar";
const BLUESOFT_PLATFORM = "bluesoft";
const TYPEFORM_PLATFORM = "typeform";
const MANDEUMZAP_PLATFORM = "mandeumzap";
const WOOCOMMERCE_PLATFORM = "woocommerce";
const ELEMENTOR_PLATFORM = "elementor";
const HOTMART_PLATFORM = "hotmart";
const SHOPIFY_PLATFORM = "shopify";
const NOTAZZ_PLATFORM = "notazz";
const EDUZZ_PLATFORM = "eduzz";
const DEFAULT_PLATFORM = "default";
const GOOGLE_ADS_PLATFORM = "google_ads";

const QIPLUS_INTEGRATED_PLATFORMS_LABELS = {
  [FORM_PLATFORM]: "Forms",
  [SMTP_PLATFORM]: "SMTP Mailer",
  [MAIL_DOMAIN_PLATFORM]: "Dominio Próprio",
  [GMAIL_SMTP_PLATFORM]: "Gmail SMTP",
  [RDSTATION_PLATFORM]: "RD Station",
  [MAUTIC_PLATFORM]: "Mautic",
  [INFUSIONSOFT_PLATFORM]: "Infusion Soft",
  [LEADLOVERS_PLATFORM]: "Lead Lovers",
  [FACEBOOK_CAPI_PLATFORM]: "API de Conversões do FB",
  [FACEBOOK_LEADS_PLATFORM]: "Facebook Leads",
  [FACEBOOK_PAGES_PLATFORM]: "Facebook Pages",
  [FACEBOOK_ADS_PLATFORM]: "Facebook Ads",
  [G_FORMS_PLATFORM]: "Google Forms",
  [G_CALENDAR_PLATFORM]: "Google Calendar",
  [BLUESOFT_PLATFORM]: "BlueSoft",
  [TYPEFORM_PLATFORM]: "Typeform",
  [MANDEUMZAP_PLATFORM]: "Mande um Zap",
  [WOOCOMMERCE_PLATFORM]: "WooCommerce",
  [ELEMENTOR_PLATFORM]: "Elementor Form",
  [HOTMART_PLATFORM]: "Hotmart",
  [SHOPIFY_PLATFORM]: "Shopify",
  [NOTAZZ_PLATFORM]: "Notazz",
  [EDUZZ_PLATFORM]: "Eduzz",
  [GOOGLE_ADS_PLATFORM]: "Google ADS",
  [DEFAULT_PLATFORM]: "Default",
};

const QIPLUS_INTEGRATED_PLATFORMS_CONFIG = [
  {
    platform: FORM_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[FORM_PLATFORM],
    active: true,
    tags: true,
    importTags: false,
    associateFields: true,
    webhook: true,
    events: false /* true */,
  },
  {
    platform: SMTP_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[SMTP_PLATFORM],
    active: true,
    tags: false,
    importTags: false,
    associateFields: false,
    webhook: false,
    events: false,
  },
  {
    platform: MAIL_DOMAIN_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[MAIL_DOMAIN_PLATFORM],
    active: true,
    tags: false,
    importTags: false,
    associateFields: false,
    webhook: false,
    events: false,
  },
  {
    platform: GMAIL_SMTP_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[GMAIL_SMTP_PLATFORM],
    active: false,
    tags: false,
    importTags: false,
    associateFields: false,
    webhook: false,
    events: false,
  },
  {
    platform: RDSTATION_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[RDSTATION_PLATFORM],
    active: false,
    tags: true,
    importTags: true,
    associateFields: true,
    webhook: false,
    events: false,
  },
  {
    platform: MAUTIC_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[MAUTIC_PLATFORM],
    active: false,
    tags: false,
    importTags: false,
    associateFields: false,
    webhook: false,
    events: false,
  },
  {
    platform: INFUSIONSOFT_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[INFUSIONSOFT_PLATFORM],
    active: false,
    tags: true,
    importTags: true,
    associateFields: true,
    webhook: false,
    events: false,
  },
  {
    platform: LEADLOVERS_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[LEADLOVERS_PLATFORM],
    active: false,
    tags: false,
    importTags: false,
    associateFields: false,
    webhook: false,
    events: false,
  },
  {
    platform: FACEBOOK_CAPI_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[FACEBOOK_CAPI_PLATFORM],
    active: false,
    tags: false,
    importTags: false,
    associateFields: false,
    webhook: false,
    events: false,
  },
  {
    platform: FACEBOOK_LEADS_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[FACEBOOK_LEADS_PLATFORM],
    active: true,
    tags: true,
    importTags: false,
    associateFields: true,
    webhook: false,
    events: false,
  },
  {
    platform: FACEBOOK_PAGES_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[FACEBOOK_PAGES_PLATFORM],
    active: true,
    tags: false,
    importTags: false,
    associateFields: false,
    webhook: false,
    events: false,
  },
  {
    platform: FACEBOOK_ADS_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[FACEBOOK_ADS_PLATFORM],
    active: false, // TODO: habilitar apos aprovacao da meta
    tags: false,
    importTags: false,
    associateFields: false,
    webhook: false,
    events: false,
  },
  {
    platform: G_FORMS_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[G_FORMS_PLATFORM],
    active: true,
    tags: true,
    importTags: false,
    associateFields: true,
    webhook: false,
    events: false,
  },
  {
    platform: G_CALENDAR_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[G_CALENDAR_PLATFORM],
    active: true,
    tags: false,
    importTags: false,
    associateFields: false,
    webhook: false,
    events: false,
  },
  {
    platform: BLUESOFT_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[BLUESOFT_PLATFORM],
    active: false,
    tags: false,
    importTags: false,
    associateFields: false,
    webhook: false,
    events: false,
  },
  {
    platform: TYPEFORM_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[TYPEFORM_PLATFORM],
    active: true,
    tags: true,
    importTags: false,
    associateFields: true,
    webhook: true,
    events: false,
  },
  {
    platform: WOOCOMMERCE_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[WOOCOMMERCE_PLATFORM],
    active: true,
    tags: true,
    importTags: false,
    associateFields: true,
    webhook: true,
    events: false /* true */,
  },
  {
    platform: ELEMENTOR_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[ELEMENTOR_PLATFORM],
    active: true,
    tags: true,
    importTags: false,
    associateFields: true,
    webhook: true,
    events: false /* true */,
  },
  {
    platform: HOTMART_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[HOTMART_PLATFORM],
    active: true,
    tags: true,
    importTags: false,
    associateFields: true,
    webhook: true,
    events: false /* true */,
  },
  {
    platform: SHOPIFY_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[SHOPIFY_PLATFORM],
    active: true,
    tags: true,
    importTags: false,
    associateFields: true,
    webhook: true,
    events: false /* true */,
  },
  {
    platform: NOTAZZ_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[NOTAZZ_PLATFORM],
    active: true,
    tags: false,
    importTags: false,
    associateFields: false,
    webhook: false,
    events: false,
  },
  {
    platform: EDUZZ_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[EDUZZ_PLATFORM],
    active: true,
    tags: true,
    importTags: false,
    associateFields: true,
    webhook: true,
    events: false /* true */,
  },
  {
    platform: GOOGLE_ADS_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[GOOGLE_ADS_PLATFORM],
    active: true,
    tags: false,
    importTags: false,
    associateFields: false,
    webhook: false,
    events: false /* true */,
  },
  {
    platform: DEFAULT_PLATFORM,
    label: QIPLUS_INTEGRATED_PLATFORMS_LABELS[DEFAULT_PLATFORM],
    active: true,
    tags: true,
    importTags: false,
    associateFields: true,
    webhook: true,
    events: false /* true */,
  },
];

const CONSTANTS = {
  APP_ID,
  DEV_APP_ID,
  SITE_URL,
  DEV_URL,
  LOCAL_URL,
  APP_URL,
  DEV_APP_URL,
  LOCAL_APP_URL,
  CHAT_APP_URL,
  CHAT_APP_ENDPOINTS,
  DNS_NS1_ADDRESS,
  DNS_NS2_ADDRESS,
  SERVER_IP_ADDRESS,
  ENV_URL,
  ENV_APP_URL,
  REMOTE_URL,
  REMOTE_APP_URL,
  TRACK_URL,
  PIXEL_TRACK_URL,
  LINK_TRACK_URL,
  CHATAPI_LINK,
  CHAT_KEY,
  BANNER_TRACK_URL,
  VIDEO_TRACK_URL,
  ALL_TRACK_URL_PARAMS,
  SUBSCRIPTION_URL,
  TUTORIALS_URL,
  FIREBASE_ENV,
  NODE_ENV,
  APP_ENVIROINMENT,
  APP_ENV,
  PRODUCTION_ENV,
  DEVELOPMENT_ENV,
  PROD_ENV,
  DEV_ENV,
  IS_LOCAL_DEV,
  IS_REMOTE_DEV,
  STORED_ENV,
  QIPLUS_TRACK_MARKUP_START,
  QIPLUS_TRACK_MARKUP_END,
  QIPLUS_PIXEL_MARKUP_START,
  QIPLUS_PIXEL_MARKUP_END,
  QIPLUS_SUBSCRIPTION_MARKUP_START,
  QIPLUS_SUBSCRIPTION_MARKUP_END,
  QIPLUS_CLOUD_DOMAIN,
  EMAIL_APP_DOMAIN,
  MAILING_DOMAIN,
  DEV_MAILING_DOMAIN,
  DEFAULT_MAILING_DOMAIN,
  DEFAULT_FROM_EMAIL,
  qiplusEmailDomains,
  WEBMASTER_ID,
  WEBMASTER_EMAIL,
  WEBMASTER_ACCOUNT_ID,
  DEFAULT_OWNER,
  ORPHANS_OWNER,
  NO_OWNER,
  ORPHANS_ACCOUNT,
  NO_ACCOUNT,
  DEFAULT_LANG,
  DEFAULT_LOCALE,
  MOMENT_ISO,
  MOMENT_ISO_MINUTES,
  MOMENT_ISO_HOURS,
  MOMENT_ISO_DAY,
  MOMENT_ISO_WEEK,
  MOMENT_ISO_MONTH,
  MOMENT_ISO_YEAR,
  MOMENT_LOCAL,
  MOMENT_SHORT,
  MOMENT_SHORT_DAY,
  MOMENT_SHORT_MONTH,
  MOMENT_DAY_MONTH,
  MOMENT_RFC2822,
  MOMENT_DEBUG,
  MOMENT_BR_OFFSET,
  WP_AJAX_URL,
  WEBHOOKS_URL,
  WOOCOMMERCE_WEBHOOK_URL,
  HOTMART_WEBHOOK_URL,
  SHOPIFY_WEBHOOK_URL,
  EDUZZ_WEBHOOK_URL,
  FORM_WEBHOOK_URL,
  FACEBOOK_LEADS_WEBHOOK_URL,
  WP_EDIT_POST_URL,
  NOTAZZ_NFSE_POSTBACK_URL,
  NOTAZZ_NFE_POSTBACK_URL,
  NOTAZZ_QIPLUS_API_KEY,
  AJAX_ACTION_CREATE_NOTAZZ_NFSE,
  AJAX_ACTION_CREATE_NOTAZZ_NFE,
  AJAX_ACTION_GET_NOTAZZ_NFSE,
  AJAX_ACTION_GET_NOTAZZ_NFE,
  PAGARME_ENCRYPTION_KEY,
  PAGARME_SUBSCRIPTION_POSTBACK_URL,
  PAGARME_TRANSACTION_POSTBACK_URL,
  PAGARME_IMPLEMENTATION_POSTBACK_URL,
  PAGARME_PARCELA_POSTBACK_URL,
  PAGARME_UPGRADE_POSTBACK_URL,
  PAGARME_RECIPIENT_POSTBACK_URL,
  PAGARME_API_PLANS_URL,
  PAGARME_API_PLANS_PARAMS,
  PAGARME_GATEWAY_BOLETO,
  PAGARME_GATEWAY_CREDIT_CARD,
  PAGARME_GATEWAY_CURRENT_CARD,
  PAGARME_PAID_STATUS,
  PAGARME_TRIALING_STATUS,
  PAGARME_PENDING_PAYMENT_STATUS,
  PAGARME_UNPAID_STATUS,
  PAGARME_CANCELED_STATUS,
  PAGARME_ENDED_STATUS,
  PAGARME_SUBSCRIPTION_STATUSES,
  PAGARME_TRANSACTIONS_STATUSES,
  PAGARME_RECIPIENT_QIPLUS,
  IMAP_OPTION,
  GMAIL_APP_DOMAIN,
  GOOGLE_APIS_CLIENT_ID,
  GOOGLE_APIS_CLIENT_SRC,
  GOOGLE_APIS_PLATFORM_SRC,
  GMAIL_APP_DISCOVERY_DOCS,
  GMAIL_APP_SCOPES,
  BASE64_NPM_SCR,
  BITLY_TOKEN,
  HOTMAIL_APP_DOMAIN,
  LIVE_APP_DOMAIN,
  OUTLOOK_APP_DOMAIN,
  MSN_APP_DOMAIN,
  MSA_APP_DOMAINS,
  MSA_API_CLIENT_ID,
  MSA_API_REDIRECT_URI,
  MSA_API_TOKEN_SRC,
  MSA_API_AUTHORIZE_SRC,
  MSA_GRAPH_API_SRC,
  QIPLUS_APP_ORIGIN,
  SEND_SMTP_MAIL_ACTION,
  AJAX_ACTION_CREATE_PLAN,
  AJAX_ACTION_UPDATE_PLAN,
  AJAX_ACTION_CREATE_SUBSCRIPTION,
  AJAX_ACTION_UPDATE_SUBSCRIPTION,
  CRONJOB_TYPES,
  CRONJOB_SUBJECTS,
  STARTER_PLAN_ID,
  PRO_PLAN_ID,
  CORTEX_PLAN_ID,
  SINAPSES_PLAN_ID,
  STARTER_PLAN_SLUG,
  PRO_PLAN_SLUG,
  CORTEX_PLAN_SLUG,
  SINAPSES_PLAN_SLUG,
  STARTER_PLAN_URL,
  PRO_PLAN_URL,
  CORTEX_PLAN_URL,
  SINAPSES_PLAN_URL,
  qiplusPlansRoutes,
  GTM_ID,
  FUNCTIONS_EXECUTION_LIMIT,
  FIRESTORE_MAX_LIMIT,
  FIRESTORE_MAX_DOCUMENT_SIZE,
  FIRESTORE_SAFE_DOCUMENT_FIELD_SIZE,
  YEARLY,
  MONTHLY,
  timer_1_Second,
  timer_2_Seconds,
  timer_3_Seconds,
  timer_4_Seconds,
  timer_5_Seconds,
  timer_10_Seconds,
  timer_30_Seconds,
  timer_1_Minute,
  timer_5_Minutes,
  timer_10_Minutes,
  timer_30_Minutes,
  timer_1_Hour,
  authPaths,
  frontendPaths,
  QIPLUS_STORE_ID,
  URL_PARAMS_TAG_ID,
  URL_PARAMS_PARENT_ID,
  URL_PARAMS_AFFILIATE_ID,
  URL_PARAMS_DOC_ID,
  URL_PARAMS_ACCOUNT_ID,
  URL_PARAMS_COLLECTION,
  SHORT_URL_PARAMS,
  DOC_DOESNT_EXIST,
  CREATED_FIELD,
  UPDATED_FIELD,
  PUBLISH_STATUS,
  DRAFT_STATUS,
  TRASH_STATUS,
  ACTIVE_STATUS,
  INACTIVE_STATUS,
  DELETED_STATUS,
  NAMES_FIELDS_GROUP,
  CONTACT_FIELDS_GROUP,
  PROFESSIONAL_FIELDS_GROUP,
  OTHER_FIELDS_GROUP,
  ADDRESS_FIELDS_GROUP,
  QIUSERS_FIELDS_GROUP,
  SOCIALNEWTORKS_FIELDS_GROUP,
  QI_FIELDS_GROUP,
  DATE_FIELDS_GROUP,
  CONTRACT_FIELDS_GROUP,
  TICKET_FIELDS_GROUP,
  CUSTOM_FIELDS_GROUP,
  TEAM_FIELDS_GROUP,
  QIUSERS_FIELDS_PATH,
  ADDRESS_FIELDS_PATH,
  CUSTOM_FIELDS_PATH,
  CONTRACT_FIELDS_PATH,
  TICKET_FIELDS_PATH,
  CONTRACT_NUMBER_FIELD,
  CONTRACT_MODIFIED_FIELD,
  CONTRACT_DATE_FIELD,
  TICKET_ITEMS_FIELD,
  TICKET_NUMBER_FIELD,
  TICKET_GATEWAY_FIELD,
  TICKET_TOTAL_FIELD,
  TICKET_MODIFIED_FIELD,
  TICKET_DATE_FIELD,
  ID_FIELD,
  LOG_ID_FIELD,
  LOG_RELATED_ID_FIELD,
  SCORE_FIELD,
  TYPE_FIELD,
  DATE_FIELD,
  MODIFIED_FIELD,
  QRCODE_FIELD,
  AVATAR_FIELD,
  DATEREGISTERED_FIELD,
  DESCRIPTION_FIELD,
  MARITAL_STATUS_FIELD,
  GENDER_FIELD,
  SENT_PLUSDATE_FIELD,
  SENT_FULLDATE_FIELD,
  SENT_DATE_FIELD,
  SENT_DAY_FIELD,
  SENT_MONTH_FIELD,
  SENT_YEAR_FIELD,
  OPERATOR_ID_FIELD,
  SELLER_FIELD,
  SELLERNAME_FIELD,
  SELLEREMAIL_FIELD,
  SELLERMOBILE_FIELD,
  SELLERPHONE_FIELD,
  PROMOTER_FIELD,
  PROMOTERNAME_FIELD,
  PROMOTEREMAIL_FIELD,
  PROMOTERMOBILE_FIELD,
  PROMOTERPHONE_FIELD,
  MANAGER_FIELD,
  MANAGERNAME_FIELD,
  MANAGEREMAIL_FIELD,
  MANAGERMOBILE_FIELD,
  MANAGERPHONE_FIELD,
  ROLES_FIELD,
  LEVEL_FIELD,
  EMAIL_FIELD,
  VALID_EMAIL_FIELD,
  CONTACT_FIELD,
  QIUSER_FIELD,
  AUTHOR_FIELD,
  OWNER_FIELD,
  TEAM_FIELD,
  AFFILIATE_FIELD,
  PARENT_FIELD,
  ACCOUNT_FIELD,
  CAMPAIGN_FIELD,
  LANDING_PAGE_FIELD,
  AUTOMATION_FIELD,
  CONTACT_ID_FIELD,
  FUNNEL_ID_FIELD,
  STORES_FIELD,
  COLLECTION_FIELD,
  TRACK_PARAM_REFERRER,
  TRACK_PARAM_USER_AGENT,
  TRACK_PARAM_REMOTE_ADDR,
  TRACK_PARAMS,
  KEYWORDS_FIELD,
  KEYWORDS_FIELDKEY_FIELD,
  RELATIONAL_FIELDS,
  RELATIONAL_USERS_FIELDS,
  RELATIONAL_USERS_COLLECTIONS,
  RELATIONAL_COLLECTIONS,
  LOCATION_FIELDS_MAP,
  ADDRESS_FIELDS_MAP,
  SHORTCODE_PREFIX,
  SHORTCODE_SUFIX,
  SHORTCODE_PATH_SEPARATOR,
  SHORTCODE_LABEL_SEPARATOR,
  SHORTCODE_PARAMS_SEPARATOR,
  ADDRESS_SHORTCODES_FIELDS_MAP,
  SINGLE_PRODUCT_MODE,
  CART_MODE,
  SINGLE_VALUE_MODE,
  VALUES_MODE,
  DEFAULT_SALES_MODE,
  EQUAL_MODE,
  RANDOM_MODE,
  FIRST_STAGE,
  WON_STAGE,
  LOST_STAGE,
  FUNNEL_STAGES_CONSTANTS,
  EMAIL_TRIGGERS,
  APP_TRIGGERS,
  CONDITIONAL_TRIGGERS,
  RELATIONAL_TRIGGERS,
  AppTriggersColors,
  APP_ACTION_EMAIL,
  APP_ACTION_NOTIFICATE_SELLER,
  APP_ACTION_NOTIFICATE_MANAGER,
  APP_ACTION_CONTRACT,
  APP_ACTION_SEGMENTATIONS,
  APP_ACTION_TEAMS,
  APP_ACTION_SET_SELLER,
  APP_ACTION_SET_MANAGER,
  APP_ACTION_EVENT,
  APP_ACTION_CAMPAIGN,
  APP_ACTION_GO_TO_ACTION,
  APP_ACTION_GO_TO_AUTOMATION,
  APP_ACTION_EVALUATE,
  APP_ACTION_WEBHOOK,
  APP_ACTION_DEAL,
  APP_ACTION_DEAL_UPDATE,
  APP_ACTION_DEAL_UPDATE_IN_FUNNEL,
  APP_ACTION_DEAL_WEBHOOK,
  APP_ACTION_FUNNEL_DEAL,
  APP_ACTION_FUNNEL,
  APP_ACTION_FUNNEL_SWITCHER,
  APP_ACTION_FUNNEL_PROGGRESS,
  APP_ACTION_FUNNEL_REGGRESS,
  APP_ACTION_STORE,
  APP_ACTION_CREATE_TICKET,
  APP_ACTION_USER_TASKLIST,
  APP_ACTION_TEAM_TASKLIST,
  APP_ACTION_DEAL_TASKLIST,
  APP_ACTION_TAGS,
  APP_ACTION_REMOVE_TAGS,
  APP_ACTION_DEALS_TAGS,
  APP_ACTION_REMOVE_DEALS_TAGS,
  APP_ACTION_PROFILE,
  APP_ACTION_SCORE,
  APP_ACTION_TIMER,
  APP_ACTION_SCHEDULE,
  APP_ACTION_LOG,
  APP_ACTIONS,
  SMTP_ENABLED,
  MAILING_QIPLUS_DISABLED,
  OTHER_SMTP,
  QIPLUS_SMTP,
  MAILBOXES_SMTP,
  MAIL_DOMAIN,
  MAILGUN_SERVICE,
  RESEND_SERVICE,
  QIPLUS_SMTP_OPTIONS,
  FORM_PLATFORM,
  SMTP_PLATFORM,
  MAIL_DOMAIN_PLATFORM,
  GMAIL_SMTP_PLATFORM,
  RDSTATION_PLATFORM,
  MAUTIC_PLATFORM,
  INFUSIONSOFT_PLATFORM,
  LEADLOVERS_PLATFORM,
  FACEBOOK_CAPI_PLATFORM,
  FACEBOOK_LEADS_PLATFORM,
  FACEBOOK_PAGES_PLATFORM,
  FACEBOOK_ADS_PLATFORM,
  G_FORMS_PLATFORM,
  G_CALENDAR_PLATFORM,
  BLUESOFT_PLATFORM,
  TYPEFORM_PLATFORM,
  MANDEUMZAP_PLATFORM,
  WOOCOMMERCE_PLATFORM,
  ELEMENTOR_PLATFORM,
  HOTMART_PLATFORM,
  SHOPIFY_PLATFORM,
  NOTAZZ_PLATFORM,
  EDUZZ_PLATFORM,
  GOOGLE_ADS_PLATFORM,
  DEFAULT_PLATFORM,
  QIPLUS_INTEGRATED_PLATFORMS_LABELS,
  QIPLUS_INTEGRATED_PLATFORMS_CONFIG,
  GOOGLE_ADS_CREDENTIALS,
};

module.exports = CONSTANTS;
