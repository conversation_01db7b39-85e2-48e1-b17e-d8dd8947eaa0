/**
 * Users Roles
 *
 * @note
 * This file should be synced between the qiplus-frontend and the qiplus-firebase repos.
 *
 * Before making any changes to this file, please make sure the workspace.js script is
 * running on your environment.
 *
 * qiplus-frontend/src/constants/UsersRoles.js > qiplus-firebase/functions/constants/roles.js
 */

const WEBMASTER_ROLE = 'webmaster'
const ADMIN_ROLE = 'admin'
const AFFILIATE_ROLE = 'affiliate'
const OWNER_ROLE = 'owner'
const MANAGER_ROLE = 'manager'
const OPERATOR_ROLE = 'operator'
const SELLER_ROLE = 'seller'
const PROMOTER_ROLE = 'promoter'
const LEAD_ROLE = 'lead'

const ACCOUNT_OWNERS_ROLES = [WEBMASTER_ROLE, ADMIN_ROLE, OWNER_ROLE]

const QIPLUS_ROLES_LEVELS = {
  [WEBMASTER_ROLE]: 1,
  [ADMIN_ROLE]: 2,
  [AFFILIATE_ROLE]: 20,
  [OWNER_ROLE]: 30,
  [MANAGER_ROLE]: 40,
  [OPERATOR_ROLE]: 50,
  [SELLER_ROLE]: 60,
  [PROMOTER_ROLE]: 70,
}

const WEBMASTER_LEVEL = QIPLUS_ROLES_LEVELS[WEBMASTER_ROLE]
const ADMIN_LEVEL = QIPLUS_ROLES_LEVELS[ADMIN_ROLE]
const AFFILIATE_LEVEL = QIPLUS_ROLES_LEVELS[AFFILIATE_ROLE]
const OWNER_LEVEL = QIPLUS_ROLES_LEVELS[OWNER_ROLE]
const MANAGER_LEVEL = QIPLUS_ROLES_LEVELS[MANAGER_ROLE]
const OPERATOR_LEVEL = QIPLUS_ROLES_LEVELS[OPERATOR_ROLE]
const SELLER_LEVEL = QIPLUS_ROLES_LEVELS[SELLER_ROLE]
const PROMOTER_LEVEL = QIPLUS_ROLES_LEVELS[PROMOTER_ROLE]
const LEAD_LEVEL = 100

const QIPLUS_ROLES_TO_WP_ROLES = {
  [WEBMASTER_ROLE]: 'administrator',
  [ADMIN_ROLE]: 'editor',
  [AFFILIATE_ROLE]: 'conveniado',
  [OWNER_ROLE]: 'conveniado',
  [MANAGER_ROLE]: 'author',
  [OPERATOR_ROLE]: 'contributor',
  [SELLER_ROLE]: 'contributor',
  [PROMOTER_ROLE]: 'contributor',
  [LEAD_ROLE]: 'subscriber',
}

const QIPLUS_ROLES_HERARCHY = {}
const QIPLUS_ROLES_PLURAL = {}
const QIPLUS_ROLES_FIELDS = {}

Object.keys(QIPLUS_ROLES_LEVELS).forEach(role => {
  const level = QIPLUS_ROLES_LEVELS[role]

  QIPLUS_ROLES_HERARCHY[level] = role
  QIPLUS_ROLES_PLURAL[role] = role + 's'
  QIPLUS_ROLES_FIELDS[role] = role + 's'
})

const COMMISSIONED_ROLES = [
  MANAGER_ROLE,
  SELLER_ROLE,
  PROMOTER_ROLE,
]

const QIPLUS_ROLES_RELATIONS = {
  [WEBMASTER_ROLE]: [],
  [ADMIN_ROLE]: [],
  [AFFILIATE_ROLE]: [],
  [OWNER_ROLE]: ['leads', 'tickets', 'deals', 'funnels', 'contracts'],
  [MANAGER_ROLE]: ['leads', 'tickets', 'deals', 'funnels', 'contracts'],
  [OPERATOR_ROLE]: [],
  [SELLER_ROLE]: ['leads', 'tickets', 'deals', 'funnels', 'contracts'],
  [PROMOTER_ROLE]: ['leads'],
  [LEAD_ROLE]: [],
}

const ROLES = {
  WEBMASTER_ROLE,
  ADMIN_ROLE,
  AFFILIATE_ROLE,
  OWNER_ROLE,
  MANAGER_ROLE,
  SELLER_ROLE,
  PROMOTER_ROLE,
  OPERATOR_ROLE,
  LEAD_ROLE,
  [WEBMASTER_ROLE]: WEBMASTER_ROLE,
  [ADMIN_ROLE]: ADMIN_ROLE,
  [AFFILIATE_ROLE]: AFFILIATE_ROLE,
  [OWNER_ROLE]: OWNER_ROLE,
  [MANAGER_ROLE]: MANAGER_ROLE,
  [SELLER_ROLE]: SELLER_ROLE,
  [PROMOTER_ROLE]: PROMOTER_ROLE,
  [OPERATOR_ROLE]: OPERATOR_ROLE,
  [LEAD_ROLE]: LEAD_ROLE,
  ACCOUNT_OWNERS_ROLES,
  WEBMASTER_LEVEL,
  ADMIN_LEVEL,
  AFFILIATE_LEVEL,
  OWNER_LEVEL,
  MANAGER_LEVEL,
  OPERATOR_LEVEL,
  SELLER_LEVEL,
  PROMOTER_LEVEL,
  LEAD_LEVEL,
  QIPLUS_ROLES_HERARCHY,
  QIPLUS_ROLES_TO_WP_ROLES,
  QIPLUS_ROLES_PLURAL,
  QIPLUS_ROLES_FIELDS,
  QIPLUS_ROLES_LEVELS,
  QIPLUS_ROLES_RELATIONS,
  COMMISSIONED_ROLES,
}

module.exports = ROLES
