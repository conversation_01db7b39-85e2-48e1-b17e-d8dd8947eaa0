const redis = require("redis");
const dotenv = require("dotenv");

// Carregar variáveis de ambiente do arquivo .env
dotenv.config();

/**
 * Configurações do Redis
 * Utilizando variáveis de ambiente para maior segurança e flexibilidade
 */
const REDIS_CONFIG = {
  socket: {
    host: process.env.REDIS_HOST || "localhost", // Host do Redis
    port: parseInt(process.env.REDIS_PORT || "6379"), // Porta padrão do Redis
    connectTimeout: parseInt(process.env.REDIS_TIMEOUT || "10000"), // Timeout de conexão em ms (10 segundos)
    reconnectStrategy: (retries) => {
      // Estratégia de reconexão com backoff exponencial
      const delay = Math.min(
        // Começar com 100ms, dobrar a cada tentativa, máximo de 10 segundos
        Math.pow(2, retries) * 100,
        10000
      );
      console.log(
        `SHOTXCRON > Redis reconnection attempt ${retries + 1}, delay: ${delay}ms`
      );
      return delay;
    },
  },
  password: process.env.REDIS_PASSWORD || "", // Senha para autenticação no Redis
  database: parseInt(process.env.REDIS_DATABASE || "0"), // Banco de dados padrão
  // Configurações adicionais para melhorar a estabilidade
  disableOfflineQueue: false, // Permitir enfileirar comandos quando desconectado
  enableReadyCheck: true, // Verificar se o Redis está pronto antes de aceitar comandos
  maxRetriesPerRequest: 3, // Número máximo de tentativas por comando
};

// Variável para armazenar a instância do cliente Redis
let redisClient = null;

/**
 * Inicializa e retorna um cliente Redis com tratamento de erros e reconexão
 * @returns {Promise<Object|null>} Cliente Redis ou null em caso de erro
 */
const getRedisClient = async () => {
  // Se já existe um cliente conectado, retorná-lo
  if (redisClient && redisClient.isOpen) {
    return redisClient;
  }

  // Se existe um cliente mas não está conectado, tentar reconectar
  if (redisClient && !redisClient.isOpen) {
    try {
      console.log(
        "SHOTXCRON > REDIS > Attempting to reconnect existing client"
      );
      await redisClient.connect();
      return redisClient;
    } catch (error) {
      console.error(
        "SHOTXCRON > REDIS > Failed to reconnect existing client:",
        error.message
      );
      // Continuar para criar um novo cliente
      redisClient = null;
    }
  }

  try {
    console.log(
      `SHOTXCRON > REDIS > Creating new connection to ${REDIS_CONFIG.socket.host}:${REDIS_CONFIG.socket.port}`
    );

    // Criar um novo cliente Redis com as configurações definidas
    redisClient = redis.createClient(REDIS_CONFIG);

    // Configurar handlers de eventos para melhor observabilidade
    redisClient.on("error", (error) => {
      console.error("SHOTXCRON > REDIS > Error:", error.message);

      // Tratamento específico para erros comuns
      if (error.message.includes("NOAUTH")) {
        console.error(
          "SHOTXCRON > REDIS > Authentication Error: Check password configuration"
        );
      } else if (error.message.includes("ECONNREFUSED")) {
        console.error(
          "SHOTXCRON > REDIS > Connection Error: Server unreachable"
        );
      } else if (error.message.includes("ETIMEDOUT")) {
        console.error(
          "SHOTXCRON > REDIS > Timeout Error: Connection timed out"
        );
      }
    });

    redisClient.on("connect", () => {
      console.log("SHOTXCRON > REDIS > Connected successfully");
    });

    redisClient.on("ready", () => {
      console.log("SHOTXCRON > REDIS > Client authenticated and ready to use");
    });

    redisClient.on("reconnecting", () => {
      console.log("SHOTXCRON > REDIS > Attempting to reconnect...");
    });

    redisClient.on("end", () => {
      console.log("SHOTXCRON > REDIS > Connection closed");
      redisClient = null;
    });

    // Conectar ao servidor Redis
    await redisClient.connect();

    // Verificar se a conexão está realmente estabelecida
    if (!redisClient.isOpen) {
      throw new Error("Failed to establish connection");
    }

    console.log("SHOTXCRON > REDIS > Connection established and ready");
    return redisClient;
  } catch (error) {
    console.error(
      "SHOTXCRON > REDIS > Failed to create client:",
      error.message
    );
    console.error(error.stack);

    // Limpar a referência para permitir nova tentativa
    redisClient = null;
    return null;
  }
};

/**
 * Salva uma mensagem no Redis com um tempo de expiração
 * @param {string} key - Chave para armazenar a mensagem
 * @param {Object} message - Objeto da mensagem a ser armazenado
 * @param {number} expireInSeconds - Tempo de expiração em segundos (opcional)
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const saveMessage = async (key, message, expireInSeconds = null) => {
  try {
    const client = await getRedisClient();
    if (!client) {
      console.error("SHOTXCRON > Redis client not available");
      return false;
    }

    // Converter o objeto para string JSON
    const messageStr = JSON.stringify(message);

    // Salvar no Redis (API moderna usa apenas .set())
    const options = {};
    if (expireInSeconds && !isNaN(expireInSeconds)) {
      options.EX = expireInSeconds;
    }

    await client.set(key, messageStr, options);

    return true;
  } catch (error) {
    console.error("SHOTXCRON > Error saving message to Redis:", error);
    return false;
  }
};

/**
 * Salva uma mensagem em uma lista ordenada por tempo no Redis
 * @param {string} listKey - Chave da lista ordenada
 * @param {number} score - Pontuação para ordenação (timestamp)
 * @param {string} messageKey - Chave única da mensagem
 * @param {Object} message - Objeto da mensagem a ser armazenado
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const saveScheduledMessage = async (listKey, score, messageKey, message) => {
  try {
    const client = await getRedisClient();
    if (!client) {
      console.error("SHOTXCRON > SAVEREDIS > Redis client not available");
      return false;
    }

    // Converter o score (timestamp) para uma data legível
    const scoreDate = new Date(score).toISOString();

    console.log(
      "SHOTXCRON > SAVEREDIS > BEFORE SAVE",
      "key",
      messageKey,
      "message",
      message,
      "listkey",
      listKey,
      "score",
      score,
      "scoreDate",
      scoreDate
    );

    // Converter o objeto para string JSON
    const messageStr = JSON.stringify(message);

    // Salvar a mensagem com sua chave
    await client.set(messageKey, messageStr);

    // Adicionar à lista ordenada (API moderna usa .zAdd())
    await client.zAdd(listKey, [
      {
        score: score,
        value: messageKey,
      },
    ]);

    return true;
  } catch (error) {
    console.error(
      "SHOTXCRON > SAVEREDIS > Error saving scheduled message to Redis:",
      error
    );
    return false;
  }
};

/**
 * Obtém mensagens agendadas até um determinado timestamp
 * @param {string} listKey - Chave da lista ordenada
 * @param {number} maxScore - Pontuação máxima (timestamp)
 * @param {Object} options - Opções adicionais
 * @param {boolean} options.remove - Se true, remove as mensagens da lista após obtê-las (default: true)
 * @param {number} options.limit - Número máximo de mensagens a retornar (default: 100)
 * @returns {Promise<Array>} - Lista de mensagens
 */
const getScheduledMessages = async (
  listKey,
  maxScore,
  origin = "",
  options = {}
) => {
  // Definir opções padrão
  const { remove = true, limit = 100 } = options;

  console.log("SHOTXCRON > GETREDIS > START > ORIGIN", {
    origin,
  });

  try {
    // Obter cliente Redis
    const client = await getRedisClient();
    if (!client) {
      console.error(
        "SHOTXCRON > GETREDIS > Client not available for getting scheduled messages"
      );
      return [];
    }

    // Validar parâmetros
    if (!listKey) {
      console.error(
        "SHOTXCRON > GETREDIS > Missing list key for scheduled messages"
      );
      return [];
    }

    if (!maxScore || isNaN(maxScore)) {
      console.error(
        "SHOTXCRON > GETREDIS > Invalid max score for scheduled messages"
      );
      return [];
    }

    console.log(
      `SHOTXCRON > GETREDIS > SEARCHING FOR MESSAGES SCHEDULED UNTIL (${new Date(maxScore).toISOString()})`
    );

    // Obter chaves das mensagens até o timestamp especificado com limite
    const messageKeys = await client.zRange(listKey, 0, maxScore, {
      BY: "SCORE",
      LIMIT: {
        offset: 0,
        count: limit,
      },
    });

    // Se não houver mensagens, retornar array vazio
    if (!messageKeys || messageKeys.length === 0) {
      console.log("SHOTXCRON > GETREDIS > No scheduled messages found");
      return [];
    }

    console.log(
      `SHOTXCRON > GETREDIS > Found ${messageKeys.length} scheduled messages to process`
    );

    // Obter as mensagens em lote usando pipeline para melhor performance
    const pipeline = client.multi();

    // Adicionar comandos GET para cada chave
    messageKeys.forEach((key) => {
      pipeline.get(key);
    });

    // Executar pipeline
    const messageResults = await pipeline.exec();

    // Processar resultados
    const messages = [];
    const failedKeys = [];
    console.log(
      `SHOTXCRON > GETREDIS > RESULTS > Processing allMessages scheduled messages`,
      messageResults
    );
    for (let i = 0; i < messageResults.length; i++) {
      const messageStr = messageResults[i];
      const key = messageKeys[i];

      if (messageStr) {
        try {
          const message = JSON.parse(messageStr);

          // Adicionar a chave Redis original à mensagem para facilitar a exclusão posterior
          message.redis_key = key;

          messages.push(message);
        } catch (e) {
          console.error(
            `SHOTXCRON > GETREDIS > Error parsing message with key ${key}:`,
            e.message
          );
          failedKeys.push(key);
        }
      } else {
        console.warn(
          `SHOTXCRON > GETREDIS > Message with key ${key} not found`
        );
        // failedKeys.push(key);
        // removeAllMessages();
      }
    }

    console.log(
      `SHOTXCRON > GETREDIS > Successfully processed ${messages.length} messages`
    );
    return messages;
  } catch (error) {
    console.error(
      "SHOTXCRON > GETREDIS > Error getting scheduled messages:",
      error.message
    );
    console.error(error.stack);
    return [];
  }
};

/**
 * Remove uma mensagem específica do Redis
 * @param {string} messageKey - Chave da mensagem a ser removida
 * @param {string} listKey - Chave da lista ordenada (default: 'shotx:scheduled_messages')
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const removeMessage = async (
  messageKey,
  listKey = "shotx:scheduled_messages"
) => {
  try {
    const client = await getRedisClient();
    if (!client) {
      console.error(
        "SHOTXCRON > REDIS > Client not available for removing message"
      );
      return false;
    }

    // Verificar se a mensagem existe
    const exists = await client.exists(messageKey);
    if (!exists) {
      console.error(`SHOTXCRON > REDIS > Message ${messageKey} not found`);
      return false;
    }

    // Remover a mensagem da lista ordenada
    await client.zRem(listKey, messageKey);

    // Remover o conteúdo da mensagem
    await client.del(messageKey);

    console.log(
      `SHOTXCRON > REDIS > Successfully removed message ${messageKey}`
    );
    return true;
  } catch (error) {
    console.error(
      `SHOTXCRON > REDIS > Error removing message ${messageKey}:`,
      error.message
    );
    return false;
  }
};

/**
 * Remove todas as mensagens do Redis
 * @param {string} listKey - Chave da lista ordenada (default: 'shotx:scheduled_messages')
 * @returns {Promise<number>} - Número de mensagens removidas
 */
const removeAllMessages = async (listKey = "shotx:scheduled_messages") => {
  try {
    const client = await getRedisClient();
    if (!client) {
      console.error(
        "SHOTXCRON > REDIS > Client not available for removing messages"
      );
      return 0;
    }

    // Obter todas as chaves de mensagens
    const messageKeys = await client.zRange(listKey, 0, -1);

    if (!messageKeys || messageKeys.length === 0) {
      console.log("SHOTXCRON > REDIS > No messages to remove");
      return 0;
    }

    console.log(`SHOTXCRON > REDIS > Removing ${messageKeys.length} messages`);

    // Remover todas as mensagens em lote
    if (messageKeys.length > 0) {
      // Remover as chaves da lista ordenada
      await client.zRem(listKey, messageKeys);

      // Remover o conteúdo de cada mensagem
      await client.del(messageKeys);
    }

    console.log(
      `SHOTXCRON > REDIS > Successfully removed ${messageKeys.length} messages`
    );
    return messageKeys.length;
  } catch (error) {
    console.error(
      "SHOTXCRON > REDIS > Error removing all messages:",
      error.message
    );
    return 0;
  }
};

module.exports = {
  getRedisClient,
  saveMessage,
  saveScheduledMessage,
  getScheduledMessages,
  removeMessage,
  removeAllMessages,
};
