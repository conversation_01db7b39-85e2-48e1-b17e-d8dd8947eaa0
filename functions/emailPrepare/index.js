const {
  saveScheduledMessage,
} = require("../utils/redisClient");
const { FirestoreRef, CONSTANTS } = require("../init");
const { momentNow } = require("../helpers");
const moment = require("moment");
const dotenv = require("dotenv");

// Carregar variáveis de ambiente do arquivo .env
dotenv.config();

/**
 * Prepara e armazena emails no Redis para envio posterior
 * @param {Array} emails - Lista de emails para processar
 * @returns {Promise<Array>} - Lista de emails preparados e salvos
 */
const emailPrepareMessages = async (emails) => {
  if (!emails || emails.length === 0) {
    console.log(
      "EMAILCRON > EMAIL PREPARE > PREPARE EMAILS > NO EMAILS TO PROCESS"
    );
    return [];
  }

  console.log(
    `EMAILCRON > EMAIL PREPARE > PREPARE EMAILS > Processing ${emails.length} emails`
  );

  // Processar emails em paralelo
  const savedEmails = await Promise.all(
    emails.map((email) => prepareEmailAndSaveInRedis(email))
  );

  // Filtrar emails que foram salvos com sucesso
  const successfulEmails = savedEmails.filter(Boolean);

  console.log(
    `EMAILCRON > EMAIL PREPARE > PREPARE EMAILS > Successfully prepared ${successfulEmails.length} emails`
  );

  return successfulEmails;
};

/**
 * Prepara um email individual e salva no Redis
 * @param {Object} emailData - Dados do email
 * @returns {Promise<Object|null>} - Email preparado ou null em caso de erro
 */
const prepareEmailAndSaveInRedis = async (emailData) => {
  try {
    if (!emailData || !emailData.to) {
      console.log(
        "EMAILCRON > EMAIL PREPARE > PREPARE EMAIL > Invalid email data - missing recipient"
      );
      return null;
    }

    console.log(
      `EMAILCRON > EMAIL PREPARE > PREPARE EMAIL > Processing email to ${emailData.to}`
    );

    // Preparar dados do email com valores padrão
    const preparedEmail = {
      to: emailData.to,
      from: emailData.from || "",
      fromName: emailData.fromName || "",
      cc: emailData.cc || "",
      bcc: emailData.bcc || "",
      subject: emailData.subject || "Email sem assunto",
      html: emailData.html || emailData.message || "",
      scheduled_date: emailData.scheduled_date || new Date().toISOString(),
      context: emailData.context || {},
      owner: emailData.owner || "",
      accountId: emailData.accountId || "",
      emailVars: emailData.emailVars || null,
      smtp: emailData.smtp || CONSTANTS.QIPLUS_SMTP,
      smtp_integration: emailData.smtp_integration || "",
      created_at: new Date().toISOString(),
      attempts: 0,
      // Dados específicos para tracking
      mailId: emailData.mailId || `email_${Date.now()}`,
      triggerId: emailData.triggerId || `trigger_${Date.now()}`,
    };

    // Validar dados obrigatórios
    if (!preparedEmail.html) {
      console.error(
        "EMAILCRON > EMAIL PREPARE > PREPARE EMAIL > ERROR: Email content (html) is required"
      );
      return null;
    }

    // Determinar o tempo de agendamento
    let scheduledTime;
    if (emailData.scheduled_date) {
      scheduledTime = new Date(emailData.scheduled_date).getTime();
    } else {
      // Se não especificado, agendar para agora
      scheduledTime = new Date().getTime();
    }

    // Gerar ID único para o email
    const emailId = `${preparedEmail.mailId}_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

    // Salvar no Redis
    const saved = await saveEmailInRedis(
      emailId,
      preparedEmail,
      scheduledTime
    );

    if (saved) {
      console.log(
        `EMAILCRON > EMAIL PREPARE > PREPARE EMAIL > SUCCESS: Email saved with ID ${emailId} for time ${new Date(scheduledTime).toISOString()}`
      );
      return {
        ...preparedEmail,
        id: emailId,
        redis_key: `email:${emailId}`,
        scheduled_timestamp: scheduledTime,
      };
    } else {
      console.error(
        `EMAILCRON > EMAIL PREPARE > PREPARE EMAIL > ERROR: Failed to save email with ID ${emailId}`
      );
      return null;
    }
  } catch (error) {
    console.error("EMAILCRON > EMAIL PREPARE > PREPARE EMAIL > ERROR:", error);
    console.error(error.stack);
    return null;
  }
};

/**
 * Salva um email no Redis com um timestamp para agendamento
 * @param {string} emailId - ID único do email
 * @param {Object} email - Objeto do email
 * @param {number} scheduledTime - Timestamp de agendamento
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const saveEmailInRedis = async (emailId, email, scheduledTime) => {
  try {
    // Validar parâmetros
    if (!emailId) {
      console.error(
        "EMAILCRON > EMAIL PREPARE > SAVE EMAIL > ERROR: Email ID is required"
      );
      return false;
    }

    if (!email) {
      console.error(
        "EMAILCRON > EMAIL PREPARE > SAVE EMAIL > ERROR: Email content is required"
      );
      return false;
    }

    if (!scheduledTime || isNaN(scheduledTime)) {
      console.error(
        "EMAILCRON > EMAIL PREPARE > SAVE EMAIL > ERROR: Valid scheduled time is required"
      );
      return false;
    }

    // Chaves Redis
    const scheduledListKey = "emails:scheduled_emails";
    const emailKey = `email:${emailId}`;

    // Enriquecer o email com informações adicionais
    const enrichedEmail = {
      ...email,
      id: emailId,
      redis_key: emailKey,
      scheduled_timestamp: scheduledTime,
      scheduled_date_readable: new Date(scheduledTime).toISOString(),
      saved_at: new Date().toISOString(),
    };

    console.log(
      `EMAILCRON > EMAIL PREPARE > SAVE EMAIL > Saving email with key ${emailKey} for time ${new Date(scheduledTime).toISOString()}`
    );

    // Salvar o email na lista ordenada
    const result = await saveScheduledMessage(
      scheduledListKey,
      scheduledTime,
      emailKey,
      enrichedEmail
    );

    if (result) {
      console.log(
        `EMAILCRON > EMAIL PREPARE > SAVE EMAIL > SUCCESS: Email saved with key ${emailKey} for time ${new Date(scheduledTime).toISOString()}`
      );
    } else {
      console.error(
        `EMAILCRON > EMAIL PREPARE > SAVE EMAIL > ERROR: Failed to save email with key ${emailKey}`
      );
    }

    return result;
  } catch (error) {
    console.error("EMAILCRON > EMAIL PREPARE > SAVE EMAIL > ERROR:", error);
    console.error(error.stack);
    return false;
  }
};

/**
 * Prepara o tempo de agendamento baseado em diferentes formatos de entrada
 * @param {string|Date|number} time - Tempo em diferentes formatos
 * @returns {number} - Timestamp em milissegundos
 */
const prepareTime = (time) => {
  try {
    if (!time) {
      return new Date().getTime();
    }

    // Se já é um timestamp
    if (typeof time === 'number') {
      return time;
    }

    // Se é uma string ou Date
    const parsedTime = new Date(time);
    if (isNaN(parsedTime.getTime())) {
      console.warn(
        `EMAILCRON > EMAIL PREPARE > PREPARE TIME > Invalid time format: ${time}, using current time`
      );
      return new Date().getTime();
    }

    return parsedTime.getTime();
  } catch (error) {
    console.error("EMAILCRON > EMAIL PREPARE > PREPARE TIME > ERROR:", error);
    return new Date().getTime();
  }
};

module.exports = {
  emailPrepareMessages,
  prepareEmailAndSaveInRedis,
  saveEmailInRedis,
  prepareTime,
};
