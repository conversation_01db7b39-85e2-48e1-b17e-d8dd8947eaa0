const {
  getScheduledMessages,
  removeMessage,
  removeAllMessages,
} = require("../utils/redisClient");
const { FirestoreRef, CONSTANTS } = require("../init");
const { momentNow } = require("../helpers");
const moment = require("moment");
const { sendMail } = require("../mailing");
const dotenv = require("dotenv");

// Carregar variáveis de ambiente do arquivo .env
dotenv.config();

/**
 * Função principal para processar e enviar emails do Redis
 * @param {Object} options - Opções de processamento
 * @returns {Promise<Array>} - Lista de emails processados
 */
const emailSendMessages = async (options = {}) => {
  try {
    // Processar os emails agendados
    const processedEmails = await processScheduledEmails(options);

    console.log(
      `EMAILCRON > EMAIL SEND MESSAGES > Processados ${processedEmails.length} emails`
    );

    return processedEmails;
  } catch (error) {
    console.error(
      "EMAILCRON > EMAIL SEND MESSAGES > Erro no processamento:",
      error.message
    );
    console.error(error.stack);
    return [];
  }
};

/**
 * Processa emails agendados do Redis e os envia
 * @param {Object} options - Opções de processamento
 * @param {number} options.batchSize - Número máximo de emails a processar por vez (default: 50)
 * @param {boolean} options.dryRun - Se true, não envia os emails, apenas simula (default: false)
 * @param {boolean} options.simulateOnly - Se true, apenas simula o envio com logs detalhados (default: false)
 * @returns {Promise<Array>} Lista de emails processados
 */
const processScheduledEmails = async (options = {}) => {
  // Definir opções padrão
  const { batchSize = 50, dryRun = false, simulateOnly = false } = options;

  console.log(
    `EMAILCRON > PROCESS EMAILS > Starting with options: batchSize=${batchSize}, dryRun=${dryRun}, simulateOnly=${simulateOnly}`
  );

  try {
    // Chave da lista ordenada de emails agendados
    const scheduledListKey = "emails:scheduled_emails";

    let momentNowISO = momentNow().format(CONSTANTS.MOMENT_ISO);
    let momentNowInstanceTimestamp = new Date(momentNowISO).getTime();

    console.log(
      `EMAILCRON > PROCESS EMAILS > Current time: ${momentNowISO} (${momentNowInstanceTimestamp})`
    );

    // Obter os emails agendados no Redis
    const emails = await getScheduledMessages(
      scheduledListKey,
      momentNowInstanceTimestamp,
      "PROCESS",
      {
        limit: batchSize,
        remove: false,
      }
    );

    if (emails.length === 0) {
      console.log("EMAILCRON > PROCESS EMAILS > NO EMAILS");
      return [];
    }

    const totalEmails = emails.length;
    console.log(
      `EMAILCRON > PROCESS EMAILS > TOTAL EMAILS ${totalEmails} TO PROCESS`
    );

    // Processar emails em paralelo
    const emailPromises = emails.map(async (email, index) => {
      try {
        const emailId =
          email.id ||
          `email_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

        console.log(
          `EMAILCRON > PROCESS EMAILS > Processing email ${index + 1}/${totalEmails}: ${emailId} to ${email.to}`
        );

        // Converter timestamp para data legível para logs
        const scheduledDate = email.scheduled_timestamp
          ? new Date(email.scheduled_timestamp).toISOString()
          : "Unknown";

        console.log(
          `EMAILCRON > PROCESS EMAILS > Email ${emailId} scheduled for: ${scheduledDate}`
        );

        // Registrar o email no Firestore antes de enviar
        await FirestoreRef.collection("emails_sent")
          .doc(emailId)
          .set({
            ...email,
            _processing_started_at: momentNow().format(CONSTANTS.MOMENT_ISO),
            status: "processing",
            _dry_run: dryRun,
            _simulate_only: simulateOnly,
          });

        // Se for modo de simulação, não enviar realmente
        if (dryRun || simulateOnly) {
          console.log(
            `EMAILCRON > PROCESS EMAILS > [${dryRun ? "DRY RUN" : "SIMULATE"}] Would send email ${emailId} to ${email.to}`
          );
          console.log(
            `EMAILCRON > PROCESS EMAILS > [${dryRun ? "DRY RUN" : "SIMULATE"}] Subject: ${email.subject}`
          );
          console.log(
            `EMAILCRON > PROCESS EMAILS > [${dryRun ? "DRY RUN" : "SIMULATE"}] From: ${email.from} (${email.fromName})`
          );

          // Atualizar status no Firestore
          await FirestoreRef.collection("emails_sent")
            .doc(emailId)
            .update({
              status: "simulated",
              _processed_at: momentNow().format(CONSTANTS.MOMENT_ISO),
            });

          // Usar a redis_key do email para remoção correta
          if (email.redis_key) {
            await removeMessage(email.redis_key, "emails:scheduled_emails");
            console.log(
              `EMAILCRON > PROCESS EMAILS > [${dryRun ? "DRY RUN" : "SIMULATE"}] Removed email ${emailId} from Redis`
            );
          }

          return { ...email, id: emailId, status: "simulated" };
        }

        // Enviar o email usando a função existente do sistema de mailing
        console.log(
          `EMAILCRON > PROCESS EMAILS > Sending email ${emailId} to ${email.to}`
        );

        const emailRef = FirestoreRef.collection("emails_sent").doc(emailId);
        const sendResult = await sendMail(email, emailRef);

        if (sendResult && sendResult.success !== false) {
          console.log(
            `EMAILCRON > PROCESS EMAILS > Email ${emailId} sent successfully`
          );

          // Atualizar status no Firestore
          await FirestoreRef.collection("emails_sent")
            .doc(emailId)
            .update({
              status: "sent",
              _processed_at: momentNow().format(CONSTANTS.MOMENT_ISO),
              _send_result: sendResult,
            });

          // Remover do Redis após envio bem-sucedido
          if (email.redis_key) {
            await removeMessage(email.redis_key, "emails:scheduled_emails");
            console.log(
              `EMAILCRON > PROCESS EMAILS > Email ${emailId} removed from Redis after successful sending`
            );
          }

          return { ...email, id: emailId, status: "sent", result: sendResult };
        } else {
          console.error(
            `EMAILCRON > PROCESS EMAILS > Failed to send email ${emailId}:`,
            sendResult
          );

          // Atualizar status no Firestore
          await FirestoreRef.collection("emails_sent")
            .doc(emailId)
            .update({
              status: "failed",
              _processed_at: momentNow().format(CONSTANTS.MOMENT_ISO),
              _error: sendResult,
            });

          // Não remover do Redis em caso de falha para tentar novamente
          return { ...email, id: emailId, status: "failed", error: sendResult };
        }
      } catch (error) {
        console.error(
          `EMAILCRON > PROCESS EMAILS > Error processing email:`,
          error.message
        );
        console.error(error.stack);
        return { ...email, status: "error", error: error.message };
      }
    });

    const results = await Promise.all(emailPromises);

    console.log(
      `EMAILCRON > PROCESS EMAILS > Completed processing ${results.length} emails`
    );

    return results;
  } catch (error) {
    console.error(
      "EMAILCRON > PROCESS EMAILS > Fatal error during processing:",
      error.message
    );
    console.error(error.stack);
    return [];
  }
};

/**
 * Limpa todos os emails agendados do Redis
 * @returns {Promise<number>} - Número de emails removidos
 */
const clearAllEmails = async () => {
  try {
    console.log("EMAILCRON > CLEAR ALL EMAILS > Iniciando limpeza de emails");

    // Chave da lista ordenada de emails agendados
    const scheduledListKey = "emails:scheduled_emails";

    // Remover todos os emails
    const removedCount = await removeAllMessages(scheduledListKey);

    console.log(
      `EMAILCRON > CLEAR ALL EMAILS > Removidos ${removedCount} emails`
    );

    return removedCount;
  } catch (error) {
    console.error("EMAILCRON > CLEAR ALL EMAILS > ERROR:", error);
    return 0;
  }
};

/**
 * Remove um email específico do Redis
 * @param {string} emailId - ID do email a ser removido
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const deleteEmail = async (emailId) => {
  try {
    console.log(`EMAILCRON > DELETE EMAIL > Removendo email ${emailId}`);

    // Chave do email
    const emailKey = `email:${emailId}`;

    // Chave da lista ordenada de emails agendados
    const scheduledListKey = "emails:scheduled_emails";

    // Remover o email
    const success = await removeMessage(emailKey, scheduledListKey);

    if (success) {
      console.log(
        `EMAILCRON > DELETE EMAIL > Email ${emailId} removido com sucesso`
      );
    } else {
      console.error(
        `EMAILCRON > DELETE EMAIL > Falha ao remover email ${emailId}`
      );
    }

    return success;
  } catch (error) {
    console.error(`EMAILCRON > DELETE EMAIL > ERROR:`, error);
    return false;
  }
};

/**
 * Lista emails agendados no Redis para visualização
 * @param {Object} options - Opções de listagem
 * @param {number} options.limit - Limite de emails a listar (default: 100)
 * @returns {Promise<Array>} Lista de emails agendados
 */
const listScheduledEmails = async (options = {}) => {
  try {
    const { limit = 100 } = options;

    console.log(
      `EMAILCRON > LIST SCHEDULED EMAILS > Listing up to ${limit} scheduled emails`
    );

    // Chave da lista ordenada de emails agendados
    const scheduledListKey = "emails:scheduled_emails";

    // Obter emails agendados (usar timestamp muito alto para pegar todos)
    const futureTimestamp = new Date().getTime() + 365 * 24 * 60 * 60 * 1000; // 1 ano no futuro

    const emails = await getScheduledMessages(
      scheduledListKey,
      futureTimestamp,
      "LIST",
      {
        limit: limit,
        remove: false,
      }
    );

    console.log(
      `EMAILCRON > LIST SCHEDULED EMAILS > Found ${emails.length} scheduled emails`
    );

    // Adicionar informações legíveis de data
    const emailsWithReadableDates = emails.map((email) => ({
      ...email,
      scheduled_date_readable: email.scheduled_timestamp
        ? new Date(email.scheduled_timestamp).toISOString()
        : "Unknown",
      time_until_send: email.scheduled_timestamp
        ? Math.max(0, email.scheduled_timestamp - new Date().getTime())
        : 0,
    }));

    // Ordenar por timestamp de agendamento
    emailsWithReadableDates.sort(
      (a, b) => (a.scheduled_timestamp || 0) - (b.scheduled_timestamp || 0)
    );

    // Log detalhado dos emails encontrados
    emailsWithReadableDates.forEach((email, index) => {
      console.log(
        `EMAILCRON > LIST SCHEDULED EMAILS > ${index + 1}. ID: ${email.id || "Unknown"}, To: ${email.to || "Unknown"}, Scheduled: ${email.scheduled_date_readable}`
      );
    });

    return emailsWithReadableDates;
  } catch (error) {
    console.error("EMAILCRON > LIST SCHEDULED EMAILS > ERROR:", error);
    return [];
  }
};

/**
 * Obtém estatísticas dos emails no Redis
 * @returns {Promise<Object>} Estatísticas dos emails
 */
const getEmailStats = async () => {
  try {
    console.log("EMAILCRON > GET EMAIL STATS > Collecting email statistics");

    const emails = await listScheduledEmails({ limit: 1000 });
    const now = new Date().getTime();

    const stats = {
      total_emails: emails.length,
      emails_ready_to_send: emails.filter(
        (email) => email.scheduled_timestamp && email.scheduled_timestamp <= now
      ).length,
      emails_future: emails.filter(
        (email) => email.scheduled_timestamp && email.scheduled_timestamp > now
      ).length,
      emails_without_timestamp: emails.filter(
        (email) => !email.scheduled_timestamp
      ).length,
      oldest_email: emails.length > 0 ? emails[0] : null,
      newest_email: emails.length > 0 ? emails[emails.length - 1] : null,
    };

    console.log("EMAILCRON > GET EMAIL STATS > Statistics:", {
      total: stats.total_emails,
      ready: stats.emails_ready_to_send,
      future: stats.emails_future,
      no_timestamp: stats.emails_without_timestamp,
    });

    return stats;
  } catch (error) {
    console.error("EMAILCRON > GET EMAIL STATS > ERROR:", error);
    return {
      total_emails: 0,
      emails_ready_to_send: 0,
      emails_future: 0,
      emails_without_timestamp: 0,
      oldest_email: null,
      newest_email: null,
      error: error.message,
    };
  }
};

module.exports = {
  emailSendMessages,
  processScheduledEmails,
  clearAllEmails,
  deleteEmail,
  listScheduledEmails,
  getEmailStats,
};
