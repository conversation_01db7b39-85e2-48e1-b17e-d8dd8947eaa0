const { ROLES, CONSTANTS, COLLECTIONS,  FirestoreRef, helpers, moment, momentNow, langMessages } = require('../init');
const { sanitizeFieldTypes, jsonClone, createShortlink, getViewLink, nowToISO } = helpers;
const { updateRef } = require('../post');

const {
	MOMENT_ISO,
} = CONSTANTS;

const sanitizeLandingPageData = async (sanitizedData, change, context) => {

    // console.log(`sanitizeLandingPageData global > funcCounter:`, global.funcCounter);

    const { before, after } = change;
	const { params } = context;

	let promises = [];

	const docId = params.docId;
	const oldData = (before.exists && before.data()) || {};
	const docRef = after.exists && change.after.ref;

	// Exit when the data is deleted.
	if (!change.after.exists) {
		return [{}];
	}
	
	const newData = sanitizedData;
	const updatedData = {};

	const { planId, config } = newData;

	if (newData) {

		// console.log('Editing Account:', docId);
		// console.log('Editing newData:', newData);
		let viewlink = getViewLink(newData);
		if ( docId && viewlink && viewlink !== newData.viewlink ) {
			updatedData.viewlink = viewlink;
			let bitlyReq = await createShortlink(viewlink);
			if ( bitlyReq.link ) updatedData.shortlink = bitlyReq.link;
		}

		let editUrl = newData.post_id ? `${CONSTANTS.WP_EDIT_POST_URL}${newData.post_id}` : '';
		if ( editUrl !== newData.edit_url ) {
			updatedData.edit_url = editUrl;
		}

	}	
	
	// merge changes
	const mergedData = { ...newData, ...updatedData }

	// sanitize Field Types
	sanitizedData = sanitizeFieldTypes(mergedData);
	
	let hasUpdates = false;
	const updateKeys = [];

	Object.keys(sanitizedData).forEach(k=>{
		const updatedValue = sanitizedData[k];
		const newValue = newData[k];
		if ( helpers.isCyclic(newValue) || helpers.isCyclic(updatedValue) ) return;
		if ( typeof newValue !== typeof updatedValue || 
			((k in updatedData) && JSON.stringify(newValue) !== JSON.stringify(updatedValue))
		) {
			hasUpdates = true;
			updateKeys.push(k)
		}
	})
	
	let updateObj = {};
	if ( hasUpdates && change.after.exists ) {
		updateObj = { systemUpdate: true };
		updateKeys.forEach(k=>{
			updateObj[k] = sanitizedData[k];
		})
		console.log('sanitizeLandingPageData > hasUpdates', { updateKeys, updateObj });
	}
	
	// console.log('sanitizeLandingPageData > sanitizedData', sanitizedData);
	// console.log('sanitizeLandingPageData > mergedData', mergedData);

	return [updateObj, async () => {
		if( promises.length ) {
			await Promise.all(promises).catch(console.error)
		}
		return null;
	}];

};

module.exports = {
    sanitizeLandingPageData,
}