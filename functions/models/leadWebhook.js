const { jsonClone } = require('../helpers');

const { ADDRESS_FIELDS_MAP } = require('../constants');

const address = {};

ADDRESS_FIELDS_MAP.forEach(field => {
    address[field] = '';
});

const leadWebhook = {
    ID: "",                                         // (string)
    id: "",                                         // (string)
    accountId: "",                                  // (string)
    address: json<PERSON>lone(address),                    // (map)
    avatar: "",                                     // (string)
    birth: {                                        // (map)
        day: "",
        month: "",
        year: "",
    },                                             
    birthday: "",                                   // (string)
    cnpj: "",                                       // (string)
    companyName: "",                                // (string)
    cpf: "",                                        // (string)
    date: "",                                       // (string)
    description: "",                                // (string)
    displayName: "",                                // (string)
    email: "",                                      // (string)
    facebook: "",                                   // (string)
    firstName: "",                                  // (string)
    gender: "",                                     // (string)
    ie: "",                                         // (string)
    instagram: "",                                  // (string)
    lastName: "",                                   // (string)
    linkedin: "",                                   // (string)
    locale: "",                                     // (string)
    mailbox_verified: false,                        // (boolean)
    maritalStatus: "",                              // (string)
    mobile: "",                                     // (string)
    mobileCC: "",                                   // (string)
    modified: "",                                   // (string)
    nickname: "",                                   // (string) 
    occupation: "",                                 // (string) 
    phone: "",                                      // (string) 
    phoneCC: "",                                    // (string) 
    profile: "",                                    // (string) 
    qrcode: "",                                     // (string) 
    rg: "",                                         // (string) 
    score: 0,                                       // (number) 
    twitter: "",                                    // (string)
    type: "individual",                             // (string)
    url: "",                                        // (string)
    phone_numbers: []
}

module.exports = leadWebhook