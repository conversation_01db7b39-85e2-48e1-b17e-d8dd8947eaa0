const { ROLES, CONSTANTS, COLLECTIONS } = require('../init');
const { FirestoreRef, helpers, moment, momentNow, langMessages } = require('../init');
const { addNewLog, fetchPost, updatePost, updateRef } = require('../post');
const { doActions } = require('../automations');
const { getTeamMembers } = require('../qiusers');

// console.log('\n\n\ndeals ------------------------------');
// console.log({ addNewLog, fetchPost })
// console.log({ doActions })
// console.log('------------------------------');

const {
	isLoopable,
    capitalizeStr,
	sanitizeFieldTypes,
	jsonClone,
	areEqualObjects,
	nowToISO,
} = helpers;


const {
	APP_TRIGGERS,
	MOMENT_ISO,
} = CONSTANTS;


// grabHereToCopyPaste
const sanitizeDealData = async (sanitizedData, change, context) => {
	console.log('sanitizeDealData------', { before: change.before.exists, after: change.after.exists });
	
	// Exit when the data is deleted.
	if (!change.after.exists) {
		return [{}];
	}

	const { before, after } = change;
	const { params } = context;

	const { docId } = params;

	const oldData = (before.exists && before.data()) || {};
	const newData = (after.exists && after.data()) || {};
	const docRef = after.exists && change.after.ref;

	let updatedData = {};
	let updateObj = {};

	const queue = [];

	const owner = sanitizedData[CONSTANTS.OWNER_FIELD]||CONSTANTS.ORPHANS_OWNER;
	const accountId = sanitizedData[CONSTANTS.ACCOUNT_FIELD]||CONSTANTS.ORPHANS_ACCOUNT;
	const contactId = sanitizedData[CONSTANTS.CONTACT_ID_FIELD]||'';
	const funnelId = sanitizedData[CONSTANTS.FUNNEL_ID_FIELD]||'';
	
	let lead = {};
	if ( contactId ) {
		try { lead = await fetchPost(COLLECTIONS.LEADS_COLLECTION_NAME, contactId); } 
		catch (error) { console.log('SanitizeDealData > fetchPost > contactId > err', { error, contactId }); }
	}
	
	let funnel = {};
	
	if ( funnelId ) {
		try { funnel = await fetchPost(COLLECTIONS.FUNNELS_COLLECTION_NAME, funnelId, true); } 
		catch (error) { console.log('SanitizeDealData > fetchPost > funnelId > err', { error, funnelId }); }		
	}
	
	const mergedContact = {
		...(lead||{}),
		...(sanitizedData.contact||{}),
		contactId,
		ID: contactId,
		id: contactId,
		owner,
		accountId
	};

	Object.keys(mergedContact).forEach((field,i)=>{
		const val = mergedContact[field];
		if (lead[field] && val==="") {
			mergedContact[field] = lead[field];
		}
	});
	
	let title = sanitizedData.title;
	let stage = sanitizedData.stage;

	if ( change.after.exists ) {

		console.log(`SanitizeDealData > stages > docId: ${docId}`, {
			// newData,
			docId,
			newStage: sanitizedData.stage || '',
			oldStage: oldData.stage || '',
		});

		// funnel
		if ( !funnel || !funnel.ID ) {

			funnel = null;
			stage = "";
			updatedData.stage = "";
			updatedData[CONSTANTS.FUNNEL_ID_FIELD] = "";
			
		}else{
			
			const { pipeline } = funnel;

			// stage
			if ( !(stage in pipeline) ) {
				const firstStage = Object.keys(pipeline)[0];
				stage = `${firstStage}`;
				updatedData.stage = `${firstStage}`;
			}else
			if ( !isNaN(parseInt(stage)) && typeof newData.stage === "number" ) {
				updatedData.stage = `${stage}`;
			}

			if ( sanitizedData.stage === CONSTANTS.WON_STAGE && oldData.stage !== CONSTANTS.WON_STAGE ) {
				const days = moment(nowToISO()).diff(moment(sanitizedData.date),'days');
				const seconds = moment(nowToISO()).diff(moment(sanitizedData.date),'seconds');
				updatedData.stats = {
					...updatedData.stats||{},
					wonCycleDays: days,
					wonCycleSeconds: seconds
				};

				console.log(`SanitizeDealData > cycles > docId: ${docId}`, {
					// newData,
					days,
					seconds,
					secondsmomentNow: momentNow(nowToISO()).diff(moment(sanitizedData.date),'seconds'),
					secondsnowToISO: moment(nowToISO()).diff(moment(sanitizedData.date),'seconds'),
				});
		
			}
			
		}	

		// console.log('SanitizeDealData > debug',{
		// 	beforeExists: before.exists,
		// 	funnel_TEAM_FIELD: funnel && funnel[CONSTANTS.TEAM_FIELD],
		// 	deal_TEAM_FIELD: sanitizedData[CONSTANTS.TEAM_FIELD],
		// });

		if ( 
			(funnel && funnel[CONSTANTS.TEAM_FIELD] && !sanitizedData[CONSTANTS.TEAM_FIELD]) || 
			(sanitizedData[CONSTANTS.TEAM_FIELD] && !sanitizedData[CONSTANTS.SELLER_FIELD] && !sanitizedData[CONSTANTS.MANAGER_FIELD]) 
		) {

			const currentTeam = {
				[CONSTANTS.TEAM_FIELD]: sanitizedData[CONSTANTS.TEAM_FIELD]||"",
				[CONSTANTS.SELLER_FIELD]: sanitizedData[CONSTANTS.SELLER_FIELD]||"",
				[CONSTANTS.MANAGER_FIELD]: sanitizedData[CONSTANTS.MANAGER_FIELD]||"",
			};
			
			try {
				let teamMembers = await getTeamMembers({ deal: sanitizedData, funnel });
				console.log('SanitizeDealData > teamMembers',{ teamMembers });
				if (!areEqualObjects(teamMembers, currentTeam)) {
					let leadUpdates = {};
					Object.keys(teamMembers).forEach((f,i)=>{
						if (!updatedData[f] && !sanitizedData[f]) updatedData[f] = teamMembers[f];
						/* 
						| If the lead has been created deal context
						*/
						if (!lead[f] && (lead.context||{}).id === sanitizedData.ID && 
							lead.date && moment().diff(moment(lead.date), "days") === 0) {
							leadUpdates[f] = teamMembers[f];
						}
					});
					if (isLoopable(leadUpdates)) {
						queue.push(updatePost(COLLECTIONS.LEADS_COLLECTION_NAME, leadUpdates, lead.ID));
					}
				}
			} catch (error) {
				console.log('SanitizeDealData > teamMembers > error',{ error });
			}
		}
		
		// console.log('SanitizeDealData > debug',{
		// 	mergedContact_SELLER_FIELD: mergedContact[CONSTANTS.SELLER_FIELD],
		// 	mergedContact_MANAGER_FIELD: mergedContact[CONSTANTS.MANAGER_FIELD],
		// 	oldDataContactId: oldData.contactId,
		// 	newDataContactId: newData.contactId,
		// });
		
		if ( !oldData.contactId && newData.contactId && !updatedData[CONSTANTS.SELLER_FIELD] && !updatedData[CONSTANTS.MANAGER_FIELD] ) {
			
			if ( mergedContact[CONSTANTS.SELLER_FIELD] && !sanitizedData[CONSTANTS.SELLER_FIELD] ) {
				updatedData[CONSTANTS.SELLER_FIELD] = mergedContact[CONSTANTS.SELLER_FIELD];
			}
			if ( mergedContact[CONSTANTS.MANAGER_FIELD] && !sanitizedData[CONSTANTS.MANAGER_FIELD] ) {
				updatedData[CONSTANTS.MANAGER_FIELD] = mergedContact[CONSTANTS.MANAGER_FIELD];
			}

		}

		// title
		if ( !title ) {

			let titleFields = [];
			
			if ( mergedContact.displayName ) {
				titleFields.push(mergedContact.displayName);
			}else
			if ( mergedContact.firstName ) {
				titleFields.push(`${mergedContact.firstName} ${mergedContact.lastName}`);
			}
			if ( mergedContact.companyName ) titleFields.push(mergedContact.companyName);
			if ( !titleFields.length && Boolean(mergedContact.email) ) titleFields.push(mergedContact.email);

			if ( titleFields.reduce((a,b)=>a+b,'').replace(/ /g,'') ) {
				title = titleFields.join('-');
				updatedData.title = title;
			}

		}

	}

	// merge changes
	const mergedData = { ...sanitizedData, ...sanitizeFieldTypes(updatedData) };

	let hasUpdates = false;
	const updateKeys = [];

	Object.keys(mergedData).forEach(k=>{
		const updatedValue = mergedData[k];
		const newValue = sanitizedData[k];
		if ( helpers.isCyclic(newValue) || helpers.isCyclic(updatedValue) ) return;
		if ( typeof newValue !== typeof updatedValue || 
			((k in updatedData) && JSON.stringify(newValue) !== JSON.stringify(updatedValue))
		) {
			hasUpdates = true;
			updateKeys.push(k);
		}
	});
	
	// LOGS
	const logKeys = [CONSTANTS.DATE_FIELD, CONSTANTS.MODIFIED_FIELD, ...CONSTANTS.RELATIONAL_FIELDS];
	const logData = {};
	
	logKeys.forEach(key=>{
		if ( change.after.exists && (key in mergedData) ) {
			logData[key] = mergedData[key];
		}else
		if ( !change.after.exists && (key in oldData) ) {
			logData[key] = oldData[key];
		}	
	});

	const currentStage = mergedData.stage === 0 || mergedData.stage ? `${mergedData.stage}` : "";
	const prevStage = oldData.stage === 0 || oldData.stage ? `${oldData.stage}` : "";	

	const currentFunnelId = funnelId;
	const prevFunnelId = oldData[CONSTANTS.FUNNEL_ID_FIELD]||'';
	const isFunnelSwitch = (prevFunnelId && funnel && currentFunnelId !== prevFunnelId);
	const withinFunnel = currentFunnelId === oldData[CONSTANTS.FUNNEL_ID_FIELD];

	logData.stage = currentStage;
	logData.currentStage = currentStage;
	logData.prevStage = prevStage;	

	logData.currentFunnelId = currentFunnelId;
	logData.prevFunnelId = prevFunnelId;	

	logData.value = (mergedData.data||{}).value||0;
	logData.currentValue = (mergedData.data||{}).value||0;
	logData.prevValue = (oldData.data||{}).value||0;	

	const log = {
		contactId,
		user_id: contactId,
		operator_id: 0,
		id: docId,
		collection: COLLECTIONS.DEALS_COLLECTION_NAME,
		trigger: '',
		date: momentNow().format(CONSTANTS.MOMENT_ISO),
		owner,
		accountId,
		data: logData,
		context: {
			id: funnelId,
			collection: COLLECTIONS.FUNNELS_COLLECTION_NAME,
			operator_id: 0,
			[CONSTANTS.FUNNEL_ID_FIELD]: funnelId
		}
	};

	const oldDataLog = {
		...log,
		context: isFunnelSwitch ? {
			...log.context,
			id: prevFunnelId,
			[CONSTANTS.FUNNEL_ID_FIELD]: prevFunnelId
		} : log.context
	};

	const funnelLog = !funnel ? {} : {
		id: funnelId,
		collection: COLLECTIONS.FUNNELS_COLLECTION_NAME,
		context: {
			id: docId,
			collection: COLLECTIONS.DEALS_COLLECTION_NAME,
			operator_id: 0
		}
	};

	const oldDataFunnelLog = {
		...funnelLog,
		id: prevFunnelId,
	};

	console.log('sanitizeDealData > after.exists', change.after.exists);

	if ( change.after.exists ) {

		if ( before.exists ) {
			CONSTANTS.RELATIONAL_FIELDS.forEach(field=>{
				if ( (field in mergedData) && mergedData[field] !== oldData[field] ) {
					let trigger = !mergedData[field] && oldData[field] ? `${field}_removed` : `${field}_changed`;
					if (CONSTANTS.RELATIONAL_TRIGGERS[trigger]) {
						queue.push(addNewLog({
							...log,
							trigger,
							data: {
								...logData,
								[`current${capitalizeStr(field)}`]: mergedData[field],
								[`prev${capitalizeStr(field)}`]: oldData[field]||null,
							},
						}));
					}
				}
			});
		}

		console.log('sanitizeDealData > funnel', { funnel });

		if ( funnel ) {	
	
			if ( oldData[CONSTANTS.FUNNEL_ID_FIELD] !== mergedData[CONSTANTS.FUNNEL_ID_FIELD] ) {
				queue.push(addNewLog({
					...log,
					trigger: APP_TRIGGERS.APP_TRIGGER_ADDED_TO_FUNNEL,
				}));
				if (funnelId) queue.push(addNewLog({
					...log,
					...funnelLog,
					trigger: APP_TRIGGERS.APP_TRIGGER_ADDED_TO_FUNNEL,
				}));
			}
	
			/* 
			| Find out if there's any trigger to add a log
			| for automation listeners
			*/
			let trigger = '';
			if ( currentStage === CONSTANTS.WON_STAGE && prevStage !== CONSTANTS.WON_STAGE ) {
				trigger = APP_TRIGGERS.APP_TRIGGER_WON;
			}else
			if ( currentStage === CONSTANTS.LOST_STAGE && prevStage !== CONSTANTS.LOST_STAGE ) {
				trigger = APP_TRIGGERS.APP_TRIGGER_LOST;
			}else
			if ( !isNaN(parseInt(currentStage)) && !isNaN(parseInt(prevStage)) && withinFunnel && parseInt(currentStage) > parseInt(prevStage) ) {
				trigger = APP_TRIGGERS.APP_TRIGGER_PROGRESSED;
			}else
			if ( !isNaN(parseInt(currentStage)) && !isNaN(parseInt(prevStage)) && withinFunnel && parseInt(currentStage) < parseInt(prevStage) ) {
				trigger = APP_TRIGGERS.APP_TRIGGER_REGRESSED;
			}
			
			if ( trigger ) {
				queue.push(addNewLog({
					...log,
					trigger,
				}));
				if (funnelId) queue.push(addNewLog({
					...log,
					...funnelLog,
					trigger,
				}));
			}
	
			if ( isFunnelSwitch || currentStage !== prevStage ) {
				if (currentStage) {
					queue.push(addNewLog({
						...log,
						trigger: APP_TRIGGERS.APP_TRIGGER_CURRENT_STAGE,
					}));
					if (funnelId) queue.push(addNewLog({
						...log,
						...funnelLog,
						trigger: APP_TRIGGERS.APP_TRIGGER_CURRENT_STAGE,
					}));
					queue.push(addNewLog({
						...log,
						trigger: APP_TRIGGERS.APP_TRIGGER_STAGEIN,
					}));
					if (funnelId) queue.push(addNewLog({
						...log,
						...funnelLog,
						trigger: APP_TRIGGERS.APP_TRIGGER_STAGEIN,
					}));
				}
				if (prevStage) {
					queue.push(addNewLog({
						...(isFunnelSwitch ? oldDataLog : log),
						trigger: APP_TRIGGERS.APP_TRIGGER_STAGEOUT,
					}));
					if (funnelId) queue.push(addNewLog({
						...(isFunnelSwitch ? oldDataLog : log),
						...(isFunnelSwitch ? oldDataFunnelLog : funnelLog),
						trigger: APP_TRIGGERS.APP_TRIGGER_STAGEOUT,
					}));
				}
			}
	
			let actions = ((funnel||{}).actions||[]).filter(a=>('stage' in a) && a.stage.toString()===currentStage.toString());
			
			console.log('sanitizeDealData > actions',{
				actions,
				currentStage,
				prevStage,
				contactId,
				lead,
				isFunnelSwitch
			});
			
			if ( actions.length && (isFunnelSwitch || currentStage !== prevStage) ) {
	
				const isLead = contactId && lead && lead.ID;
				
				queue.push(
					new Promise(async (res,rej)=>{
						
						const results = await doActions({ contact: isLead ? lead : mergedData.contact, deal: mergedData, automation: funnel, actions });
					
						return addNewLog({
							contactId,
							user_id: contactId,
							operator_id: 0,
							id: funnelId,
							collection: COLLECTIONS.FUNNELS_COLLECTION_NAME,
							trigger: APP_TRIGGERS.APP_TRIGGER_FIRED,
							date: momentNow().format(CONSTANTS.MOMENT_ISO),
							owner,
							accountId,
							context: {
								operator_id: 0,
								id: docId,
								collection: COLLECTIONS.DEALS_COLLECTION_NAME,
							},
							data: {
								...logData,
								results: Array.isArray(results) && results.length,
								err: results===null
							},
						})
						.then(res)
						.catch(error=>{
							console.log("Add Deal Log error", { error });
							return res(false);
						});
					})
				);
			}
	
		}
	}
	
	if( hasUpdates && change.after.exists ) {
		updateObj = { systemUpdate: true };
		updateKeys.forEach(k=>{
			updateObj[k] = mergedData[k];
		});
		// queue.splice(0,0,updateRef(docRef, updateObj));
		console.log('sanitizeDealData > hasUpdates', { updateKeys, updateObj });
	}
	
	console.log(':: queue ::',queue.length);
	
	// console.log('mergedData', mergedData);
	return [updateObj, async () => {
		if( queue.length ) {
			const promises = queue.map(q=>q.collection ? fetchPost(q.collection, q.id) : q);
			await Promise.all(promises).then(results=>{
				results.forEach((post,r)=>{
					if ( queue[r].action ) {
						console.log('hasAction');					
						return queue[r].action(post);
					}
					return null;
				});
				return null;
			});	
		}
		return null;
	}];

};

// grabHereToCopyPaste
const updateFunnelStats = (where) => {

	let FunnelsRef = FirestoreRef.collection(COLLECTIONS.FUNNELS_COLLECTION_NAME);
	
	if (Array.isArray(where)) where.forEach(queryArgs => (
		FunnelsRef = FunnelsRef.where(queryArgs[0], queryArgs[1], queryArgs[2])
	));

	return FunnelsRef.get().then(snap=>{

		let promises = snap.docs.map(async doc=>{
			
			const funnel = doc.data();
			const { pipeline } = funnel;

			if (!pipeline) return null;
			
			const dealsQuery = await FirestoreRef.collection(COLLECTIONS.DEALS_COLLECTION_NAME).where(CONSTANTS.FUNNEL_ID_FIELD,"==",doc.id).get();
			const deals = dealsQuery.docs.map(d=>d.data());
			
			dealsQuery.docs.filter(d=>typeof d.data().stage==="number").forEach(d=>{
				d.ref.update({ stage: `${d.data().stage}` });
			});

			let stats = {
				deals: deals.length,
				won: deals.filter(d=>d.stage==="won").length,
				lost: deals.filter(d=>d.stage==="lost").length,
			};

			let stages = [];
			Object.keys(pipeline).filter(k=>k!==CONSTANTS.WON_STAGE&&k!==CONSTANTS.LOST_STAGE)
			.forEach(k=>{
				stats[k] = deals.filter(d=>d.stage.toString()===k.toString()).length;
				stages.push(k);
			});

			console.log('updateFunnelStats', { stats, id: doc.id, stages });

			let wonCycleDays = 0;
			let wonCycleSeconds = 0;

			deals.filter(d=>d.stage===CONSTANTS.WON_STAGE)
			.forEach((deal,i) => {
				if (helpers.isNumeric((deal.stats||{}).wonCycleDays)) {
					const days = Number(deal.stats.wonCycleDays)||0;
					wonCycleDays+=days;
				}
				if (helpers.isNumeric((deal.stats||{}).wonCycleSeconds)) {
					const seconds = Number(deal.stats.wonCycleSeconds)||0;
					wonCycleSeconds+=seconds;
				}else
				if (helpers.isNumeric((deal.stats||{}).wonCycleDays)) {
					const days = Number(deal.stats.wonCycleDays)||0;
					const seconds = days*24*60*60;
					wonCycleSeconds+=seconds;
				}
			});

			stats.wonCycleDays = wonCycleDays;
			stats.wonCycleSeconds = wonCycleSeconds;

			return doc.ref.update({ stats });
			
		});

		return Promise.all(promises);

	});
		
};

module.exports = {
	sanitizeDealData,
	updateFunnelStats
};
