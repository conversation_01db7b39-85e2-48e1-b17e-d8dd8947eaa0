const { momentNow } = require("../helpers");
const { FirestoreRef, CONSTANTS, COLLECTIONS, moment } = require("../init");
const { MOMENT_ISO } = CONSTANTS;
const axios = require("axios");
const { getLeadById, getSegmentationsByIds } = require("../post");
const { getIgIdByInstance } = require("../utils/instacesUtils");
const { getLeadsIdsFromSegmentation } = require("../leads");
const { shotxOrganizeMessages } = require("../shotxOrganize");
const { shotxSendMessages } = require("../shotxSendMessages");
const shotxCron = async () => {
  let now = momentNow().add(5, "minutes").format(MOMENT_ISO);
  console.log("SHOTXCRON > START");

  const fetchAppointments = async () => {
    let appointments = [];

    await FirestoreRef.collection(COLLECTIONS.SHOTX_CRON_COLLECTION_NAME)
      .where("status", "==", "published")
      .where("scheduled_date", "<=", now)
      .get()
      .then((snapshot) => {
        return snapshot.docs.map((doc) => {
          appointments.push(doc.data());
          doc.ref.update({
            modified_date: momentNow().format(MOMENT_ISO),
            status: "sent",
          });
          return appointments;
        });
      });
    console.log("SHOTXCRON > FETCHED", appointments.length);
    return appointments;
  };

  return fetchAppointments().then(async (appointments) => {
    if (appointments && appointments.length > 0) {
      let preparedAppointments = [];
      try {
        for (const appointment of appointments) {
          console.log("SHOTXCRON > PREPARE > APP", appointment);
          const prepared = await prepareLeadsOnAppointments(appointment);
          FirestoreRef.collection(COLLECTIONS.SHOTX_CRON_COLLECTION_NAME)
            .doc(appointment.ID)
            .update({ segmentationsIds: prepared.segmentationsIds });
          console.log("SHOTXCRON > PREPARE > PREPARED SCHEDULE", prepared);
          preparedAppointments = [
            ...preparedAppointments,
            ...prepared.preparedAppointments,
          ];
        }
      } catch (error) {
        console.log("SHOTXCRON > PREPARE > ERROR", error);
      }

      shotxOrganizeMessages(preparedAppointments);
    }
    return null;
  });
};

const prepareLeadsOnAppointments = async (appointment) => {
  const leads = appointment.contacts || [];
  console.log("SHOTXCRON > PREPARE > LEADS", leads);

  if ((!leads || leads.length === 0) && !appointment.segmentations) {
    console.log("SHOTXCRON > PREPARE > NO LEADS", appointment);
    return null;
  }

  const preparedAppointments = [];
  const contacts = [];
  const segmentationsIds = [];
  if (appointment.hasOwnProperty("segmentations")) {
    const segmentations = await getSegmentationsByIds(
      appointment.segmentations
    );

    for (const segmentation of Object.values(segmentations)) {
      let contactIds;

      if (segmentation.config.dinamic) {
        console.log("SHOTXCRON > PREPARE > SEGMENTATION DINAMIC", segmentation);
        contactIds = await getLeadsIdsFromSegmentation(segmentation);
        segmentationsIds.push(...contactIds.data.contactIds);
        leads.push(...contactIds.data.contactIds);
      } else {
        console.log("SHOTXCRON > PREPARE > SEGMENTATION STATIC", segmentation);
        contactIds = segmentation.leads;
        console.log(
          "SHOTXCRON > PREPARE > SEGMENTATION STATIC",
          segmentation.leads
        );
        leads.push(...contactIds);
        segmentationsIds.push(...contactIds);
      }
      console.log(
        "SHOTXCRON > PREPARE > LEADS IDS FROM SEGMENTATION",
        contactIds
      );
    }
  }

  // console.log("SHOTXCRON > PREPARE > segmentatios", segmentatios);

  for (const leadId of leads) {
    const lead = (await getLeadById(leadId)) || {};
    if (lead) {
      let contactRemoteId;
      let contactName;

      switch (appointment.instance.platform) {
        case "Whatsapp":
          contactName = lead.displayName;
          contactRemoteId = lead.mobileCC + lead.mobile;
          contacts.push({
            id: leadId,
            contactName,
            contactRemoteId,
          });
          break;
        case "Instagram":
          contactName = lead.displayName;
          contactRemoteId = getIgIdByInstance(lead, appointment.instance);
          contacts.push({
            id: leadId,
            contactName,
            contactRemoteId,
          });
          break;
        default:
          break;
      }
    }
  }
  const appointmentPrepared = {
    ...appointment,
    contacts,
  };

  preparedAppointments.push(appointmentPrepared);
  return {
    preparedAppointments,
    segmentationsIds,
  };
};

module.exports = {
  shotxCron,
};
