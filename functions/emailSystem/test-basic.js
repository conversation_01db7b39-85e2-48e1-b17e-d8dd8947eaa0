/**
 * Teste básico do sistema de emails Redis
 * Este teste verifica apenas as funções básicas sem depender do Firebase
 */

const dotenv = require("dotenv");

// Carregar variáveis de ambiente
dotenv.config();

// Mock básico para evitar erros do Firebase
const mockFirestore = {
  collection: () => ({
    doc: () => ({
      set: async () => ({ success: true }),
      update: async () => ({ success: true })
    })
  })
};

// Mock das dependências do Firebase
const mockInit = {
  FirestoreRef: mockFirestore,
  CONSTANTS: {
    MOMENT_ISO: "YYYY-MM-DDTHH:mm:ss.SSSZ",
    QIPLUS_SMTP: "qiplus"
  }
};

const mockHelpers = {
  momentNow: () => ({
    format: (format) => new Date().toISOString()
  })
};

// Substituir as dependências
const Module = require('module');
const originalRequire = Module.prototype.require;

Module.prototype.require = function(id) {
  if (id === '../init') {
    return mockInit;
  }
  if (id === '../helpers') {
    return mockHelpers;
  }
  if (id === '../mailing') {
    return {
      sendMail: async (email) => {
        console.log(`MOCK SEND MAIL > Enviando email para ${email.to}: ${email.subject}`);
        return { success: true, messageId: 'mock_' + Date.now() };
      }
    };
  }
  return originalRequire.apply(this, arguments);
};

/**
 * Teste das funções Redis básicas
 */
const testarRedisBasico = async () => {
  console.log('\n=== TESTE REDIS BÁSICO ===');
  
  try {
    const { getRedisClient } = require('../utils/redisClient');
    
    console.log('🔌 Testando conexão Redis...');
    const client = await getRedisClient();
    
    if (client && client.isOpen) {
      console.log('✅ Conexão Redis estabelecida com sucesso');
      
      // Teste básico de escrita/leitura
      const testKey = 'test:email_system_' + Date.now();
      const testValue = JSON.stringify({ test: true, timestamp: Date.now() });
      
      await client.set(testKey, testValue, { EX: 60 }); // Expira em 60 segundos
      const retrieved = await client.get(testKey);
      
      if (retrieved === testValue) {
        console.log('✅ Teste de escrita/leitura Redis bem-sucedido');
        await client.del(testKey); // Limpar teste
        return true;
      } else {
        console.log('❌ Falha no teste de escrita/leitura Redis');
        return false;
      }
    } else {
      console.log('❌ Falha na conexão Redis');
      return false;
    }
  } catch (error) {
    console.error('❌ Erro no teste Redis:', error.message);
    return false;
  }
};

/**
 * Teste da função emailPrepare
 */
const testarEmailPrepare = async () => {
  console.log('\n=== TESTE EMAIL PREPARE ===');
  
  try {
    const { emailPrepareMessages } = require('../emailPrepare');
    
    const emailsTeste = [
      {
        to: '<EMAIL>',
        from: '<EMAIL>',
        fromName: 'QiPlus Teste',
        subject: 'Email de Teste 1',
        html: '<h1>Teste 1</h1><p>Este é um email de teste.</p>',
        scheduled_date: new Date(Date.now() + 60000).toISOString(), // 1 minuto no futuro
        accountId: 'test_acc_1',
        owner: 'test_user_1'
      },
      {
        to: '<EMAIL>',
        from: '<EMAIL>',
        fromName: 'QiPlus Teste',
        subject: 'Email de Teste 2',
        html: '<h1>Teste 2</h1><p>Este é outro email de teste.</p>',
        // Sem scheduled_date - será enviado imediatamente
        accountId: 'test_acc_2',
        owner: 'test_user_2'
      }
    ];
    
    console.log('📧 Preparando emails de teste...');
    const resultado = await emailPrepareMessages(emailsTeste);
    
    if (resultado && resultado.length > 0) {
      console.log(`✅ ${resultado.length} emails preparados com sucesso`);
      resultado.forEach((email, index) => {
        if (email) {
          console.log(`   ${index + 1}. ID: ${email.id}, Para: ${email.to}`);
        }
      });
      return resultado;
    } else {
      console.log('❌ Falha na preparação de emails');
      return [];
    }
  } catch (error) {
    console.error('❌ Erro no teste emailPrepare:', error.message);
    return [];
  }
};

/**
 * Teste da função emailSend (modo simulação)
 */
const testarEmailSend = async () => {
  console.log('\n=== TESTE EMAIL SEND (SIMULAÇÃO) ===');
  
  try {
    const { emailSendMessages, listScheduledEmails } = require('../emailSend');
    
    // Primeiro, listar emails disponíveis
    console.log('📋 Listando emails agendados...');
    const emailsAgendados = await listScheduledEmails({ limit: 10 });
    
    if (emailsAgendados.length === 0) {
      console.log('📭 Nenhum email agendado encontrado para teste');
      return [];
    }
    
    console.log(`📧 ${emailsAgendados.length} emails encontrados`);
    
    // Executar em modo simulação
    console.log('🔍 Executando envio em modo simulação...');
    const resultado = await emailSendMessages({
      batchSize: 5,
      dryRun: true, // Modo simulação
      simulateOnly: false
    });
    
    if (resultado && resultado.length > 0) {
      console.log(`✅ ${resultado.length} emails processados em simulação`);
      resultado.forEach((email, index) => {
        if (email && email.status) {
          console.log(`   ${index + 1}. ${email.id} -> Status: ${email.status}`);
        }
      });
      return resultado;
    } else {
      console.log('❌ Nenhum email foi processado');
      return [];
    }
  } catch (error) {
    console.error('❌ Erro no teste emailSend:', error.message);
    return [];
  }
};

/**
 * Teste das estatísticas
 */
const testarEstatisticas = async () => {
  console.log('\n=== TESTE ESTATÍSTICAS ===');
  
  try {
    const { getEmailStats } = require('../emailSend');
    
    console.log('📊 Coletando estatísticas...');
    const stats = await getEmailStats();
    
    if (stats) {
      console.log('✅ Estatísticas coletadas com sucesso:');
      console.log(`   Total de emails: ${stats.total_emails}`);
      console.log(`   Prontos para envio: ${stats.emails_ready_to_send}`);
      console.log(`   Agendados para o futuro: ${stats.emails_future}`);
      console.log(`   Sem timestamp: ${stats.emails_without_timestamp}`);
      return stats;
    } else {
      console.log('❌ Falha ao coletar estatísticas');
      return null;
    }
  } catch (error) {
    console.error('❌ Erro no teste de estatísticas:', error.message);
    return null;
  }
};

/**
 * Função principal de teste
 */
const executarTestes = async () => {
  console.log('🧪 INICIANDO TESTES DO SISTEMA DE EMAILS REDIS');
  console.log('===============================================');
  
  let sucessos = 0;
  let total = 0;
  
  // Teste 1: Redis básico
  total++;
  if (await testarRedisBasico()) {
    sucessos++;
  }
  
  // Teste 2: Email Prepare
  total++;
  const emailsPreparados = await testarEmailPrepare();
  if (emailsPreparados.length > 0) {
    sucessos++;
  }
  
  // Aguardar um pouco
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Teste 3: Email Send (simulação)
  total++;
  const emailsProcessados = await testarEmailSend();
  if (emailsProcessados.length > 0) {
    sucessos++;
  }
  
  // Teste 4: Estatísticas
  total++;
  if (await testarEstatisticas()) {
    sucessos++;
  }
  
  // Resultado final
  console.log('\n📋 RESULTADO DOS TESTES');
  console.log('========================');
  console.log(`✅ Sucessos: ${sucessos}/${total}`);
  console.log(`❌ Falhas: ${total - sucessos}/${total}`);
  
  if (sucessos === total) {
    console.log('🎉 Todos os testes passaram! Sistema funcionando corretamente.');
  } else {
    console.log('⚠️  Alguns testes falharam. Verifique os logs acima.');
  }
  
  return { sucessos, total, taxa: (sucessos / total) * 100 };
};

// Executar testes se este arquivo for chamado diretamente
if (require.main === module) {
  executarTestes()
    .then((resultado) => {
      console.log(`\n🏁 Testes concluídos com ${resultado.taxa.toFixed(1)}% de sucesso.`);
      process.exit(resultado.sucessos === resultado.total ? 0 : 1);
    })
    .catch((error) => {
      console.error('\n💥 Erro fatal nos testes:', error.message);
      console.error(error.stack);
      process.exit(1);
    });
}

module.exports = {
  testarRedisBasico,
  testarEmailPrepare,
  testarEmailSend,
  testarEstatisticas,
  executarTestes,
};
