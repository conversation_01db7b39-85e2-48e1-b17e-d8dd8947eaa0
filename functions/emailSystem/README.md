# Sistema de Emails baseado em Redis

Este sistema implementa o envio de emails usando Redis como armazenamento intermediário, seguindo o mesmo padrão arquitetural do shotxSendMessages.

## Arquitetura

O sistema é dividido em duas funções principais:

### 1. emailPrepare - Preparação e Armazenamento
- **Localização**: `functions/emailPrepare/index.js`
- **Função**: Prepara emails e os armazena no Redis com timestamps
- **Chave Redis**: `emails:scheduled_emails`
- **Estrutura**: Lista ordenada (sorted set) com timestamps como scores

### 2. emailSend - Recuperação e Envio
- **Localização**: `functions/emailSend/index.js`
- **Função**: Recupera emails do Redis e os envia usando o sistema de mailing existente
- **Comportamento**: Remove emails do Redis após envio bem-sucedido

## Configuração

O sistema usa as mesmas variáveis de ambiente Redis já configuradas:

```env
REDIS_HOST=**************
REDIS_PORT=6379
REDIS_PASSWORD=senhaMuitoBo@0192
REDIS_DATABASE=0
REDIS_TIMEOUT=10000
```

## Uso

### Preparar e Armazenar Emails

```javascript
const { emailPrepareMessages } = require('./emailPrepare');

const emails = [
  {
    to: '<EMAIL>',
    from: '<EMAIL>',
    fromName: 'QiPlus',
    subject: 'Teste de Email',
    html: '<h1>Olá!</h1><p>Este é um email de teste.</p>',
    scheduled_date: '2024-01-15T10:00:00Z', // Opcional
    accountId: 'account123',
    owner: 'user123'
  }
];

const result = await emailPrepareMessages(emails);
console.log('Emails preparados:', result);
```

### Enviar Emails Agendados

```javascript
const { emailSendMessages } = require('./emailSend');

// Envio normal
const sent = await emailSendMessages({
  batchSize: 50,
  dryRun: false
});

// Modo de simulação (apenas logs, não envia)
const simulated = await emailSendMessages({
  batchSize: 10,
  dryRun: true
});

// Apenas visualizar o que seria enviado
const preview = await emailSendMessages({
  batchSize: 5,
  simulateOnly: true
});
```

### Funções Utilitárias

```javascript
const { 
  listScheduledEmails, 
  getEmailStats, 
  clearAllEmails, 
  deleteEmail 
} = require('./emailSend');

// Listar emails agendados
const emails = await listScheduledEmails({ limit: 100 });

// Obter estatísticas
const stats = await getEmailStats();

// Limpar todos os emails
const removedCount = await clearAllEmails();

// Remover email específico
const success = await deleteEmail('email_123');
```

## Estrutura de Dados

### Email no Redis
```javascript
{
  id: "email_1234567890_abc123",
  to: "<EMAIL>",
  from: "<EMAIL>",
  fromName: "QiPlus",
  subject: "Assunto do Email",
  html: "<h1>Conteúdo HTML</h1>",
  scheduled_date: "2024-01-15T10:00:00Z",
  scheduled_timestamp: *************,
  scheduled_date_readable: "2024-01-15T10:00:00.000Z",
  redis_key: "email:email_1234567890_abc123",
  created_at: "2024-01-15T09:00:00.000Z",
  saved_at: "2024-01-15T09:00:00.000Z",
  attempts: 0,
  accountId: "account123",
  owner: "user123",
  smtp: "qiplus",
  // ... outros campos
}
```

## Logs e Debug

O sistema implementa logs detalhados para facilitar o debug:

```
EMAILCRON > EMAIL PREPARE > PREPARE EMAILS > Processing 5 emails
EMAILCRON > EMAIL PREPARE > PREPARE EMAIL > Processing <NAME_EMAIL>
EMAILCRON > EMAIL PREPARE > SAVE EMAIL > Saving email with key email:email_123 for time 2024-01-15T10:00:00.000Z
EMAILCRON > EMAIL SEND MESSAGES > Processados 3 emails
EMAILCRON > PROCESS EMAILS > Email email_123 scheduled for: 2024-01-15T10:00:00.000Z
EMAILCRON > PROCESS EMAILS > Email email_123 sent successfully
```

## Integração com Sistema Existente

O sistema **NÃO modifica** a lógica de envio de emails existente em `functions/mailing/index.js`. Ele apenas:

1. Adiciona uma camada Redis para armazenamento intermediário
2. Usa a função `sendMail` existente para o envio real
3. Mantém toda a funcionalidade de tracking, validação e shortcodes
4. Preserva as configurações SMTP e integrações existentes

## Vantagens

- **Escalabilidade**: Processa emails em lotes
- **Confiabilidade**: Emails não são perdidos se o sistema falhar
- **Agendamento**: Suporte nativo para emails agendados
- **Debug**: Logs detalhados e modo de simulação
- **Compatibilidade**: Usa toda a infraestrutura de email existente
- **Flexibilidade**: Configurações por lote (batchSize, dryRun, etc.)

## Monitoramento

Use as funções de estatísticas para monitorar o sistema:

```javascript
const stats = await getEmailStats();
console.log(`Total de emails: ${stats.total_emails}`);
console.log(`Prontos para envio: ${stats.emails_ready_to_send}`);
console.log(`Agendados para o futuro: ${stats.emails_future}`);
```
