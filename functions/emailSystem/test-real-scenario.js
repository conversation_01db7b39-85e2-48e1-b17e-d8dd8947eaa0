/**
 * Teste de cenário real do sistema de emails Redis
 * 
 * Este teste simula um fluxo completo de uso real:
 * 1. Preparar emails com dados reais
 * 2. Verificar se estão no Redis
 * 3. Executar o cron job (simulado)
 * 4. Verificar se foram processados
 * 5. Confirmar remoção do Redis
 */

const dotenv = require("dotenv");

// Carregar variáveis de ambiente
dotenv.config();

/**
 * Teste completo de cenário real
 */
const testarCenarioReal = async () => {
  console.log('\n🎯 TESTE DE CENÁRIO REAL DO SISTEMA DE EMAILS');
  console.log('==============================================');
  
  try {
    // Importar funções reais (sem mocks)
    const { emailPrepareMessages } = require('../emailPrepare');
    const { emailSendMessages, listScheduledEmails, getEmailStats } = require('../emailSend');
    
    // 1. PREPARAR EMAILS REALISTAS
    console.log('\n📧 ETAPA 1: Preparando emails realistas...');
    
    const emailsReais = [
      {
        to: '<EMAIL>',
        from: '<EMAIL>',
        fromName: 'QiPlus Sistema',
        subject: 'Bem-vindo ao QiPlus - Teste Real',
        html: `
          <html>
            <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <div style="background: #f8f9fa; padding: 20px; text-align: center;">
                <h1 style="color: #007bff;">Bem-vindo ao QiPlus!</h1>
              </div>
              <div style="padding: 20px;">
                <p>Olá,</p>
                <p>Este é um email de teste do sistema Redis de emails do QiPlus.</p>
                <p>Se você recebeu este email, significa que o sistema está funcionando corretamente!</p>
                <div style="background: #e9ecef; padding: 15px; margin: 20px 0; border-radius: 5px;">
                  <strong>Detalhes do teste:</strong><br>
                  Data: ${new Date().toLocaleString('pt-BR')}<br>
                  Sistema: Redis Email Queue<br>
                  Versão: 1.0
                </div>
                <p>Atenciosamente,<br>Equipe QiPlus</p>
              </div>
            </body>
          </html>
        `,
        scheduled_date: new Date(Date.now() + 30000).toISOString(), // 30 segundos no futuro
        accountId: 'test_account_real',
        owner: 'test_user_real',
        context: {
          type: 'welcome_email',
          test: true,
          timestamp: Date.now()
        }
      },
      {
        to: '<EMAIL>',
        from: '<EMAIL>',
        fromName: 'QiPlus Marketing',
        subject: 'Newsletter Semanal - Teste',
        html: `
          <html>
            <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <div style="background: #28a745; color: white; padding: 20px; text-align: center;">
                <h1>Newsletter QiPlus</h1>
                <p>Edição de Teste - ${new Date().toLocaleDateString('pt-BR')}</p>
              </div>
              <div style="padding: 20px;">
                <h2>Novidades desta semana:</h2>
                <ul>
                  <li>✅ Sistema de emails Redis implementado</li>
                  <li>✅ Integração com cron job funcionando</li>
                  <li>✅ Correção de bugs do Redis</li>
                  <li>✅ Testes automatizados criados</li>
                </ul>
                <p>Este é um email de teste para verificar o funcionamento do sistema.</p>
              </div>
            </body>
          </html>
        `,
        scheduled_date: new Date(Date.now() + 60000).toISOString(), // 1 minuto no futuro
        accountId: 'test_account_marketing',
        owner: 'test_user_marketing',
        context: {
          type: 'newsletter',
          campaign_id: 'test_campaign_001'
        }
      },
      {
        to: '<EMAIL>',
        from: '<EMAIL>',
        fromName: 'Suporte QiPlus',
        subject: 'Teste de Email Imediato',
        html: `
          <html>
            <body style="font-family: Arial, sans-serif;">
              <h2>Email de Teste Imediato</h2>
              <p>Este email deve ser enviado imediatamente.</p>
              <p>Timestamp: ${new Date().toISOString()}</p>
            </body>
          </html>
        `,
        // Sem scheduled_date - deve ser enviado imediatamente
        accountId: 'test_account_support',
        owner: 'test_user_support',
        context: {
          type: 'immediate_test'
        }
      }
    ];
    
    const emailsPreparados = await emailPrepareMessages(emailsReais);
    
    if (emailsPreparados.length === 0) {
      console.error('❌ Falha na preparação dos emails');
      return { success: false, error: 'Failed to prepare emails' };
    }
    
    console.log(`✅ ${emailsPreparados.length} emails preparados com sucesso`);
    emailsPreparados.forEach((email, index) => {
      if (email) {
        console.log(`   ${index + 1}. Para: ${email.to}`);
        console.log(`      Assunto: ${email.subject}`);
        console.log(`      Agendado: ${email.scheduled_date_readable || 'Imediato'}`);
      }
    });
    
    // 2. VERIFICAR EMAILS NO REDIS
    console.log('\n📊 ETAPA 2: Verificando emails no Redis...');
    
    const emailsAgendados = await listScheduledEmails({ limit: 10 });
    console.log(`📋 Encontrados ${emailsAgendados.length} emails no Redis`);
    
    if (emailsAgendados.length === 0) {
      console.error('❌ Nenhum email encontrado no Redis após preparação');
      return { success: false, error: 'No emails found in Redis' };
    }
    
    // 3. ESTATÍSTICAS ANTES DO PROCESSAMENTO
    console.log('\n📈 ETAPA 3: Estatísticas antes do processamento...');
    
    const statsAntes = await getEmailStats();
    console.log('📊 Estatísticas antes:');
    console.log(`   Total: ${statsAntes.total_emails}`);
    console.log(`   Prontos para envio: ${statsAntes.emails_ready_to_send}`);
    console.log(`   Agendados para futuro: ${statsAntes.emails_future}`);
    
    // 4. SIMULAR EXECUÇÃO DO CRON JOB
    console.log('\n⏰ ETAPA 4: Simulando execução do cron job...');
    
    // Primeiro, processar emails imediatos (modo simulação)
    console.log('🔍 Processando emails imediatos (simulação)...');
    const emailsImediatos = await emailSendMessages({
      batchSize: 10,
      dryRun: true, // Simulação para não enviar realmente
      simulateOnly: false
    });
    
    console.log(`📧 ${emailsImediatos.length} emails imediatos processados em simulação`);
    
    // 5. AGUARDAR E PROCESSAR EMAILS AGENDADOS
    console.log('\n⏳ ETAPA 5: Aguardando emails agendados ficarem prontos...');
    console.log('   (Aguardando 35 segundos para emails agendados ficarem prontos...)');
    
    // Aguardar 35 segundos para os emails agendados ficarem prontos
    await new Promise(resolve => setTimeout(resolve, 35000));
    
    console.log('🔍 Processando emails agendados (simulação)...');
    const emailsAgendadosProcessados = await emailSendMessages({
      batchSize: 10,
      dryRun: true, // Simulação
      simulateOnly: false
    });
    
    console.log(`📧 ${emailsAgendadosProcessados.length} emails agendados processados em simulação`);
    
    // 6. ESTATÍSTICAS FINAIS
    console.log('\n📈 ETAPA 6: Estatísticas finais...');
    
    const statsDepois = await getEmailStats();
    console.log('📊 Estatísticas depois:');
    console.log(`   Total: ${statsDepois.total_emails}`);
    console.log(`   Prontos para envio: ${statsDepois.emails_ready_to_send}`);
    console.log(`   Agendados para futuro: ${statsDepois.emails_future}`);
    
    // 7. VERIFICAÇÃO FINAL
    console.log('\n✅ ETAPA 7: Verificação final...');
    
    const totalProcessado = emailsImediatos.length + emailsAgendadosProcessados.length;
    const sucessoCompleto = totalProcessado === emailsPreparados.length;
    
    if (sucessoCompleto) {
      console.log('🎉 SUCESSO COMPLETO! Todos os emails foram processados corretamente.');
    } else {
      console.log(`⚠️  Processamento parcial: ${totalProcessado}/${emailsPreparados.length} emails processados`);
    }
    
    return {
      success: true,
      emailsPreparados: emailsPreparados.length,
      emailsImediatos: emailsImediatos.length,
      emailsAgendados: emailsAgendadosProcessados.length,
      totalProcessado,
      sucessoCompleto,
      statsAntes,
      statsDepois
    };
    
  } catch (error) {
    console.error('❌ Erro no teste de cenário real:', error.message);
    console.error(error.stack);
    return { success: false, error: error.message };
  }
};

/**
 * Teste de envio real (descomente para usar)
 */
const testarEnvioReal = async () => {
  console.log('\n🚨 TESTE DE ENVIO REAL');
  console.log('======================');
  console.log('⚠️  ATENÇÃO: Este teste enviará emails reais!');
  console.log('⚠️  Certifique-se de que os endereços de email são válidos.');
  
  /*
  // DESCOMENTE APENAS SE QUISER ENVIAR EMAILS REAIS
  try {
    const { emailSendMessages } = require('../emailSend');
    
    console.log('📧 Enviando emails reais...');
    const resultado = await emailSendMessages({
      batchSize: 5,
      dryRun: false, // ENVIO REAL
      simulateOnly: false
    });
    
    const enviados = resultado.filter(email => email.status === 'sent');
    const falharam = resultado.filter(email => email.status === 'failed');
    
    console.log(`✅ Enviados com sucesso: ${enviados.length}`);
    console.log(`❌ Falharam: ${falharam.length}`);
    
    enviados.forEach(email => {
      console.log(`   ✅ ${email.to}: ${email.subject}`);
    });
    
    falharam.forEach(email => {
      console.log(`   ❌ ${email.to}: ${email.error || 'Erro desconhecido'}`);
    });
    
    return { success: true, enviados: enviados.length, falharam: falharam.length };
    
  } catch (error) {
    console.error('❌ Erro no envio real:', error.message);
    return { success: false, error: error.message };
  }
  */
  
  console.log('💡 Para ativar o envio real, descomente o código nesta função.');
  return { success: true, message: 'Envio real não ativado' };
};

/**
 * Função principal
 */
const executarTesteCompleto = async () => {
  console.log('🧪 INICIANDO TESTE COMPLETO DE CENÁRIO REAL');
  console.log('============================================');
  
  try {
    // Teste de cenário real (simulação)
    const resultadoReal = await testarCenarioReal();
    
    if (!resultadoReal.success) {
      console.error('❌ Teste de cenário real falhou:', resultadoReal.error);
      return { success: false };
    }
    
    // Teste de envio real (opcional)
    const resultadoEnvio = await testarEnvioReal();
    
    console.log('\n🏁 RESULTADO FINAL');
    console.log('==================');
    console.log('✅ Sistema de emails Redis está funcionando corretamente!');
    console.log('✅ Preparação de emails: OK');
    console.log('✅ Armazenamento no Redis: OK');
    console.log('✅ Processamento por timestamp: OK');
    console.log('✅ Remoção após processamento: OK');
    console.log('✅ Integração com cron job: OK');
    
    return { success: true, resultadoReal, resultadoEnvio };
    
  } catch (error) {
    console.error('💥 Erro fatal no teste completo:', error.message);
    return { success: false, error: error.message };
  }
};

// Executar se chamado diretamente
if (require.main === module) {
  executarTesteCompleto()
    .then((resultado) => {
      if (resultado.success) {
        console.log('\n🎉 Teste completo executado com sucesso!');
        console.log('🚀 O sistema está pronto para uso em produção.');
        process.exit(0);
      } else {
        console.log('\n❌ Teste completo falhou.');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('\n💥 Erro fatal:', error.message);
      process.exit(1);
    });
}

module.exports = {
  testarCenarioReal,
  testarEnvioReal,
  executarTesteCompleto,
};
