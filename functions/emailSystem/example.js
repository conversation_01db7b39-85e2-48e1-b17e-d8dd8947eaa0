/**
 * Exemplo de uso do sistema de emails baseado em Redis
 * 
 * Este arquivo demonstra como usar as funções emailPrepare e emailSend
 * seguindo o padrão arquitetural do shotxSendMessages
 */

const { emailPrepareMessages } = require('../emailPrepare');
const { 
  emailSendMessages, 
  listScheduledEmails, 
  getEmailStats, 
  clearAllEmails 
} = require('../emailSend');

/**
 * Exemplo 1: Preparar e armazenar emails no Redis
 */
const exemploPreparacaoEmails = async () => {
  console.log('\n=== EXEMPLO 1: PREPARAÇÃO DE EMAILS ===');
  
  const emails = [
    {
      to: '<EMAIL>',
      from: '<EMAIL>',
      fromName: 'QiPlus Sistema',
      subject: 'Bem-vindo ao QiPlus!',
      html: `
        <h1>Bem-vindo!</h1>
        <p>Obrigado por se cadastrar no QiPlus.</p>
        <p>Sua conta foi criada com sucesso.</p>
      `,
      scheduled_date: new Date(Date.now() + 5 * 60 * 1000).toISOString(), // 5 minutos no futuro
      accountId: 'acc_123',
      owner: 'user_456',
      context: {
        type: 'welcome_email',
        user_id: 'user_456'
      }
    },
    {
      to: '<EMAIL>',
      from: '<EMAIL>',
      fromName: 'QiPlus Marketing',
      subject: 'Novidades da semana',
      html: `
        <h1>Newsletter Semanal</h1>
        <p>Confira as novidades desta semana:</p>
        <ul>
          <li>Nova funcionalidade X</li>
          <li>Melhoria Y</li>
          <li>Correção Z</li>
        </ul>
      `,
      scheduled_date: new Date(Date.now() + 10 * 60 * 1000).toISOString(), // 10 minutos no futuro
      accountId: 'acc_123',
      owner: 'user_789',
      context: {
        type: 'newsletter',
        campaign_id: 'camp_001'
      }
    },
    {
      to: '<EMAIL>',
      from: '<EMAIL>',
      fromName: 'Suporte QiPlus',
      subject: 'Lembrete: Reunião agendada',
      html: `
        <h1>Lembrete de Reunião</h1>
        <p>Você tem uma reunião agendada para amanhã às 14:00.</p>
        <p>Link da reunião: <a href="https://meet.qiplus.com/123">Clique aqui</a></p>
      `,
      // Sem scheduled_date - será enviado imediatamente
      accountId: 'acc_456',
      owner: 'user_999',
      context: {
        type: 'meeting_reminder',
        meeting_id: 'meet_123'
      }
    }
  ];

  try {
    const resultado = await emailPrepareMessages(emails);
    console.log(`✅ ${resultado.length} emails preparados e salvos no Redis`);
    
    resultado.forEach((email, index) => {
      if (email) {
        console.log(`   ${index + 1}. ${email.id} -> ${email.to} (${email.scheduled_date_readable})`);
      }
    });
    
    return resultado;
  } catch (error) {
    console.error('❌ Erro ao preparar emails:', error.message);
    return [];
  }
};

/**
 * Exemplo 2: Listar emails agendados
 */
const exemploListarEmails = async () => {
  console.log('\n=== EXEMPLO 2: LISTAR EMAILS AGENDADOS ===');
  
  try {
    const emails = await listScheduledEmails({ limit: 10 });
    
    if (emails.length === 0) {
      console.log('📭 Nenhum email agendado encontrado');
      return;
    }
    
    console.log(`📧 ${emails.length} emails agendados encontrados:`);
    
    emails.forEach((email, index) => {
      const timeUntil = email.time_until_send > 0 
        ? `em ${Math.round(email.time_until_send / 1000 / 60)} minutos`
        : 'pronto para envio';
        
      console.log(`   ${index + 1}. Para: ${email.to}`);
      console.log(`      Assunto: ${email.subject}`);
      console.log(`      Agendado: ${email.scheduled_date_readable} (${timeUntil})`);
      console.log(`      ID: ${email.id}`);
      console.log('');
    });
    
    return emails;
  } catch (error) {
    console.error('❌ Erro ao listar emails:', error.message);
    return [];
  }
};

/**
 * Exemplo 3: Obter estatísticas dos emails
 */
const exemploEstatisticas = async () => {
  console.log('\n=== EXEMPLO 3: ESTATÍSTICAS DOS EMAILS ===');
  
  try {
    const stats = await getEmailStats();
    
    console.log('📊 Estatísticas dos emails no Redis:');
    console.log(`   Total de emails: ${stats.total_emails}`);
    console.log(`   Prontos para envio: ${stats.emails_ready_to_send}`);
    console.log(`   Agendados para o futuro: ${stats.emails_future}`);
    console.log(`   Sem timestamp: ${stats.emails_without_timestamp}`);
    
    if (stats.oldest_email) {
      console.log(`   Email mais antigo: ${stats.oldest_email.scheduled_date_readable}`);
    }
    
    if (stats.newest_email) {
      console.log(`   Email mais recente: ${stats.newest_email.scheduled_date_readable}`);
    }
    
    return stats;
  } catch (error) {
    console.error('❌ Erro ao obter estatísticas:', error.message);
    return null;
  }
};

/**
 * Exemplo 4: Enviar emails (modo simulação)
 */
const exemploEnvioSimulacao = async () => {
  console.log('\n=== EXEMPLO 4: ENVIO DE EMAILS (SIMULAÇÃO) ===');
  
  try {
    console.log('🔍 Executando em modo de simulação (não envia realmente)...');
    
    const resultado = await emailSendMessages({
      batchSize: 5,
      dryRun: true, // Modo simulação
      simulateOnly: false
    });
    
    console.log(`✅ Simulação concluída. ${resultado.length} emails processados`);
    
    resultado.forEach((email, index) => {
      if (email && email.status) {
        console.log(`   ${index + 1}. ${email.id} -> ${email.status}`);
      }
    });
    
    return resultado;
  } catch (error) {
    console.error('❌ Erro na simulação de envio:', error.message);
    return [];
  }
};

/**
 * Exemplo 5: Enviar emails reais (descomente para usar)
 */
const exemploEnvioReal = async () => {
  console.log('\n=== EXEMPLO 5: ENVIO REAL DE EMAILS ===');
  console.log('⚠️  ATENÇÃO: Este exemplo enviará emails reais!');
  console.log('⚠️  Descomente o código abaixo apenas se tiver certeza.');
  
  /*
  try {
    const resultado = await emailSendMessages({
      batchSize: 10,
      dryRun: false, // Envio real
      simulateOnly: false
    });
    
    console.log(`✅ Envio concluído. ${resultado.length} emails processados`);
    
    const enviados = resultado.filter(email => email.status === 'sent');
    const falharam = resultado.filter(email => email.status === 'failed');
    
    console.log(`📧 Enviados com sucesso: ${enviados.length}`);
    console.log(`❌ Falharam: ${falharam.length}`);
    
    return resultado;
  } catch (error) {
    console.error('❌ Erro no envio real:', error.message);
    return [];
  }
  */
  
  console.log('💡 Para ativar o envio real, descomente o código nesta função.');
  return [];
};

/**
 * Exemplo 6: Limpar todos os emails (use com cuidado!)
 */
const exemploLimparEmails = async () => {
  console.log('\n=== EXEMPLO 6: LIMPAR EMAILS ===');
  console.log('⚠️  ATENÇÃO: Esta função remove TODOS os emails do Redis!');
  console.log('⚠️  Descomente apenas se tiver certeza.');
  
  /*
  try {
    const removidos = await clearAllEmails();
    console.log(`🗑️  ${removidos} emails removidos do Redis`);
    return removidos;
  } catch (error) {
    console.error('❌ Erro ao limpar emails:', error.message);
    return 0;
  }
  */
  
  console.log('💡 Para ativar a limpeza, descomente o código nesta função.');
  return 0;
};

/**
 * Função principal que executa todos os exemplos
 */
const executarExemplos = async () => {
  console.log('🚀 INICIANDO EXEMPLOS DO SISTEMA DE EMAILS REDIS');
  console.log('================================================');
  
  try {
    // 1. Preparar emails
    await exemploPreparacaoEmails();
    
    // Aguardar um pouco para o Redis processar
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 2. Listar emails
    await exemploListarEmails();
    
    // 3. Estatísticas
    await exemploEstatisticas();
    
    // 4. Simulação de envio
    await exemploEnvioSimulacao();
    
    // 5. Envio real (comentado por segurança)
    await exemploEnvioReal();
    
    // 6. Limpeza (comentado por segurança)
    await exemploLimparEmails();
    
    console.log('\n✅ Todos os exemplos foram executados!');
    console.log('💡 Para envios reais, descomente as funções apropriadas.');
    
  } catch (error) {
    console.error('\n❌ Erro durante execução dos exemplos:', error.message);
    console.error(error.stack);
  }
};

// Executar exemplos se este arquivo for chamado diretamente
if (require.main === module) {
  executarExemplos()
    .then(() => {
      console.log('\n🏁 Execução concluída.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Erro fatal:', error.message);
      process.exit(1);
    });
}

module.exports = {
  exemploPreparacaoEmails,
  exemploListarEmails,
  exemploEstatisticas,
  exemploEnvioSimulacao,
  exemploEnvioReal,
  exemploLimparEmails,
  executarExemplos,
};
