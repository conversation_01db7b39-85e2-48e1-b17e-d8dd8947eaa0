/**
 * Teste rápido para verificar se o sistema de emails está funcionando
 * 
 * Execute este arquivo para fazer uma verificação rápida de todos os componentes
 */

const dotenv = require("dotenv");
dotenv.config();

/**
 * Teste rápido de conectividade
 */
const testeRapido = async () => {
  console.log('🚀 TESTE RÁPIDO DO SISTEMA DE EMAILS REDIS');
  console.log('==========================================');
  
  let sucessos = 0;
  let total = 0;
  
  try {
    // 1. Teste de conexão Redis
    total++;
    console.log('\n1️⃣ Testando conexão Redis...');
    try {
      const { getRedisClient } = require('../utils/redisClient');
      const client = await getRedisClient();
      
      if (client && client.isOpen) {
        console.log('   ✅ Redis conectado com sucesso');
        sucessos++;
      } else {
        console.log('   ❌ Falha na conexão Redis');
      }
    } catch (error) {
      console.log('   ❌ Erro na conexão Redis:', error.message);
    }
    
    // 2. Teste de importação das funções
    total++;
    console.log('\n2️⃣ Testando importação das funções...');
    try {
      const { emailPrepareMessages } = require('../emailPrepare');
      const { emailSendMessages } = require('../emailSend');
      
      if (typeof emailPrepareMessages === 'function' && typeof emailSendMessages === 'function') {
        console.log('   ✅ Funções importadas com sucesso');
        sucessos++;
      } else {
        console.log('   ❌ Falha na importação das funções');
      }
    } catch (error) {
      console.log('   ❌ Erro na importação:', error.message);
    }
    
    // 3. Teste de preparação de email simples
    total++;
    console.log('\n3️⃣ Testando preparação de email...');
    try {
      const { emailPrepareMessages } = require('../emailPrepare');
      
      const emailTeste = [{
        to: '<EMAIL>',
        from: '<EMAIL>',
        subject: 'Teste Rápido',
        html: '<h1>Teste</h1>',
        accountId: 'quick_test'
      }];
      
      const resultado = await emailPrepareMessages(emailTeste);
      
      if (resultado && resultado.length > 0) {
        console.log('   ✅ Email preparado com sucesso');
        console.log(`   📧 ID: ${resultado[0].id}`);
        sucessos++;
      } else {
        console.log('   ❌ Falha na preparação do email');
      }
    } catch (error) {
      console.log('   ❌ Erro na preparação:', error.message);
    }
    
    // 4. Teste de listagem de emails
    total++;
    console.log('\n4️⃣ Testando listagem de emails...');
    try {
      const { listScheduledEmails } = require('../emailSend');
      
      const emails = await listScheduledEmails({ limit: 5 });
      
      console.log(`   📋 Encontrados ${emails.length} emails no Redis`);
      
      if (emails.length >= 0) { // Aceitar 0 ou mais emails
        console.log('   ✅ Listagem funcionando');
        sucessos++;
      } else {
        console.log('   ❌ Falha na listagem');
      }
    } catch (error) {
      console.log('   ❌ Erro na listagem:', error.message);
    }
    
    // 5. Teste de estatísticas
    total++;
    console.log('\n5️⃣ Testando estatísticas...');
    try {
      const { getEmailStats } = require('../emailSend');
      
      const stats = await getEmailStats();
      
      if (stats && typeof stats.total_emails === 'number') {
        console.log('   ✅ Estatísticas funcionando');
        console.log(`   📊 Total de emails: ${stats.total_emails}`);
        console.log(`   📊 Prontos para envio: ${stats.emails_ready_to_send}`);
        sucessos++;
      } else {
        console.log('   ❌ Falha nas estatísticas');
      }
    } catch (error) {
      console.log('   ❌ Erro nas estatísticas:', error.message);
    }
    
    // 6. Teste de integração no cron job
    total++;
    console.log('\n6️⃣ Verificando integração no cron job...');
    try {
      const fs = require('fs');
      const indexContent = fs.readFileSync('../index.js', 'utf8');
      
      const temImportacao = indexContent.includes('require("./emailSend")');
      const temChamada = indexContent.includes('emailSendMessages()');
      
      if (temImportacao && temChamada) {
        console.log('   ✅ Integração no cron job confirmada');
        sucessos++;
      } else {
        console.log('   ❌ Integração no cron job não encontrada');
        console.log(`   📝 Importação: ${temImportacao ? 'OK' : 'FALTANDO'}`);
        console.log(`   📝 Chamada: ${temChamada ? 'OK' : 'FALTANDO'}`);
      }
    } catch (error) {
      console.log('   ❌ Erro na verificação do cron job:', error.message);
    }
    
    // Resultado final
    console.log('\n📋 RESULTADO DO TESTE RÁPIDO');
    console.log('============================');
    console.log(`✅ Sucessos: ${sucessos}/${total}`);
    console.log(`❌ Falhas: ${total - sucessos}/${total}`);
    
    const porcentagem = (sucessos / total) * 100;
    
    if (sucessos === total) {
      console.log('🎉 PERFEITO! Todos os testes passaram.');
      console.log('🚀 O sistema está pronto para uso.');
    } else if (porcentagem >= 80) {
      console.log('✅ MUITO BOM! A maioria dos testes passou.');
      console.log('⚠️  Verifique os itens que falharam acima.');
    } else if (porcentagem >= 60) {
      console.log('⚠️  PARCIAL. Alguns componentes precisam de atenção.');
    } else {
      console.log('❌ PROBLEMAS. Vários componentes precisam ser corrigidos.');
    }
    
    // Próximos passos
    console.log('\n🎯 PRÓXIMOS PASSOS RECOMENDADOS:');
    
    if (sucessos === total) {
      console.log('1. Execute o teste completo: node emailSystem/test-real-scenario.js');
      console.log('2. Teste com emails reais (descomente o código de envio)');
      console.log('3. Monitore os logs do Firebase Functions em produção');
    } else {
      console.log('1. Corrija os problemas identificados acima');
      console.log('2. Execute este teste novamente');
      console.log('3. Depois execute o teste completo');
    }
    
    return { sucessos, total, porcentagem };
    
  } catch (error) {
    console.error('💥 Erro fatal no teste rápido:', error.message);
    return { sucessos: 0, total: 1, porcentagem: 0 };
  }
};

// Executar se chamado diretamente
if (require.main === module) {
  testeRapido()
    .then((resultado) => {
      process.exit(resultado.sucessos === resultado.total ? 0 : 1);
    })
    .catch((error) => {
      console.error('💥 Erro fatal:', error.message);
      process.exit(1);
    });
}

module.exports = { testeRapido };
