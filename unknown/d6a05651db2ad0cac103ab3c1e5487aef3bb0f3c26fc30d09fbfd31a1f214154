const { ROLES, CONSTANTS, COLLECTIONS,  FirestoreRef, helpers, moment, momentNow, langMessages, ERROR_TYPES } = require('../init');
const { fetchPost, fetchTaxonomy, addNewLog, addNewPost } = require('../post');
const { replaceShortCodes } = require('../shortcodes');
const { createMailing } = require('../mailing');

const { APP_TRIGGERS } = CONSTANTS;

const {
    uniqueTimestamp,
    isLoopableObject,
    nowToISO,
    areEqualObjects,
	diffObjects,
	jsonClone,
} = helpers;

let funcCounter = 0;
const currentlyUpdating = [];
const DEFAULT_CONTRACT_SUBJECT = langMessages["contracts.defaultSubject"];

const { COMMISSIONED_ROLES } = ROLES;
const COMMISSIONS_STATUSES = ['paid', 'pending_payment', 'unpaid', 'canceled'];
const TRANSACTIONS_STATUSES = ['processing', 'authorized', 'paid', 'refunded', 'waiting_payment', 'pending_refund', 'refused'];
const INITIAL_STATUS = 'unpaid';

const commissionModel = {
	id: "",
    ID: "",
    title: "",
    createdAt: "",
    updatedAt: "",

	role: "",
	qiuserId: "",
	ticketId: "",

    date: moment().format(CONSTANTS.MOMENT_ISO),
    modified: moment().format(CONSTANTS.MOMENT_ISO),
    
    collection: COLLECTIONS.COMMISSIONS_SUBCOLLECTION_NAME,

	context: {
        collection: "",
        id: "",
    },
    data: {
        total: 0,
        value: 0,
        generated_pts: 0,
        cpf: "",
        cnpf: "",
        contractId: "",
    },
	items: [],
	contact: {},
    stores: [],
    status: INITIAL_STATUS,
	transaction: {}

}

// grabHereToCopyPaste
const createCommission = async (role, qiuserId, ticketData, ticketId, commissionDoc) => {

	ticketId = ticketId || ticketData[CONSTANTS.ID_FIELD];

	let newCommission = helpers.jsonClone(commissionModel)

	const isModel = (ticketData.config||{}).use_as_template;
	const isPublished = ticketData.status===CONSTANTS.PUBLISH_STATUS;

	if ( isModel || !isPublished || !ticketId ) {
		return null;
	}

	let debug = { products: [] };
	let promises = [];

	let qiuser = await fetchPost(COLLECTIONS.QIUSERS_COLLECTION_NAME, qiuserId, true);
	
	let accountId = qiuser[CONSTANTS.ACCOUNT_FIELD] || ticketData[CONSTANTS.ACCOUNT_FIELD];
	let account = await fetchPost(COLLECTIONS.ACCOUNTS_COLLECTION_NAME, accountId, true);
	
	let total = 0;

	if ( Array.isArray(ticketData.items) ) {

		let items = ticketData.items.filter(i=>Boolean(i.product && i.price && helpers.isNumeric(i.price) && helpers.isNumeric(i.qty)));
		let productIds = [...new Set(items.map(i=>i.product))]
		let products = await Promise.all(productIds.map(pId=>fetchPost(COLLECTIONS.PRODUCTS_COLLECTION_NAME, pId, true)))
		
		debug.items = items;
		debug.productIds = productIds;
		debug.productsArr = products;

		ticketData.items.forEach(item=>{

			let commissions = {};

			let product = products.find(p=>p.ID===item.product);
			if (product && product.commissions) commissions = product.commissions;
			
			let { price, qty } = item;
			
			let subTotal = parseFloat(price)*parseFloat(qty);
			
			let commissionBase = commissions[role] || (qiuser.data||{}).commission || (account.commissions||{})[role] || 0;
			
			debug.products.push({ product, item, subTotal, commissionBase })

			if ( commissionBase ) {
				let percentage = parseFloat(commissionBase);
				let value = parseFloat((subTotal*(percentage/100)).toFixed(2));
				let commission = { percentage, value }
				newCommission.items.push({ ...item, commission })
				total+=value
			}
		})

	}else
	if ( helpers.isNumeric((ticketData.data||{}).total) ) {

		let total = parseFloat(ticketData.data.total);
		let commissionBase = (qiuser.data||{}).commission || (account.commissions||{})[role] || 0;

		debug.commissionBase = commissionBase;
		
		if ( commissionBase ) {
			let percentage = parseFloat(commissionBase);
			let value = parseFloat((total*(percentage/100)).toFixed(2));
			let commission = { percentage, value }
			newCommission.commission = commission;
			total = value
		}
		
	}

	debug.total = total;
	debug.ticketId = ticketId;
	console.log('createCommission',{ debug });

	if (total) {

		let commPath = `${COLLECTIONS.TICKETS_COLLECTION_NAME}/${ticketId}/${COLLECTIONS.COMMISSIONS_SUBCOLLECTION_NAME}`;
		let createdAt = uniqueTimestamp();
		let updatedAt = uniqueTimestamp();
		let commissionId;
		
		if ( commissionDoc && commissionDoc.data ) {
			let commData = commissionDoc.data();
			createdAt = commData.createdAt||createdAt
			commissionId = commissionDoc.id;
		}else{
			let newRef = FirestoreRef.collection(commPath).doc();
			commissionId = newRef.id;
		}

		return {
			...newCommission,
			id: commissionId,
			ID: commissionId,
			title: `Comissão ${qiuser.displayName}`,
			description: `${ticketData.title||""}`,

			createdAt,
			updatedAt,

			role,
			qiuserId,
			ticketId,

			date: moment(createdAt).format(CONSTANTS.MOMENT_ISO),
			modified: moment(createdAt).format(CONSTANTS.MOMENT_ISO),
			
			context: {
				...ticketData.context||{},
				collection: COLLECTIONS.TICKETS_COLLECTION_NAME,
				id: ticketId,
				qiuserId,
				operator_id: qiuserId
			},
			
			data: {
				...newCommission.data,
				total,
			},
			
			ticketData: ticketData.data||{},
			contact: ticketData.contact||{},
			stores: ticketData.stores||[],
			transaction: {}

		}
	}

	return null
}

// grabHereToCopyPaste
const updateComissions = async (oldData, newData, ticketId, ticketRef, before, after) => {

	let debug = {};
	let promises = [];

	const ticketCommissions = {};
	
	const context = newData.context||{};
	const isModel = (newData.config||{}).use_as_template;
	const wasModel = (oldData.config||{}).use_as_template;
	const isPublished = newData.status===CONSTANTS.PUBLISH_STATUS;
	
	const commRef = FirestoreRef.collection(`${COLLECTIONS.TICKETS_COLLECTION_NAME}/${ticketId}/${COLLECTIONS.COMMISSIONS_SUBCOLLECTION_NAME}`);

	const deleteCommission = ({ qiuserId, doc, status }) => {
		if ( status===INITIAL_STATUS ) {
			promises.push(doc.ref.delete());
			let userComm = FirestoreRef.doc(`${COLLECTIONS.QIUSERS_COLLECTION_NAME}/${qiuserId}/${COLLECTIONS.COMMISSIONS_SUBCOLLECTION_NAME}/${ticketId}`);
			promises.push(userComm.delete());
		}
	}

	const addCommission = (qiuserId, newCommission) => {
		let { status, updatedAt, context } = newCommission;
		if ( !isModel ) {
			let docRef = commRef.doc(newCommission.ID);
			promises.push(docRef.set(newCommission))
			let userComm = FirestoreRef.doc(`${COLLECTIONS.QIUSERS_COLLECTION_NAME}/${qiuserId}/${COLLECTIONS.COMMISSIONS_SUBCOLLECTION_NAME}/${ticketId}`);
			promises.push(userComm.set({ docRef, status, updatedAt, context, ticketId }));
		}
	}
	
	const updateCommission = (qiuserId, newCommission) => {
		let { status, updatedAt, context } = newCommission;
		if ( !isModel && status===INITIAL_STATUS ) {
			let docRef = commRef.doc(newCommission.ID);
			promises.push(docRef.set(newCommission))
			let userComm = FirestoreRef.doc(`${COLLECTIONS.QIUSERS_COLLECTION_NAME}/${qiuserId}/${COLLECTIONS.COMMISSIONS_SUBCOLLECTION_NAME}/${ticketId}`);
			promises.push(userComm.set({ docRef, status, updatedAt, context, ticketId }));
		}
	}
	
	if ( before.exists ) {

		let commQuery = await commRef.get();

		debug.currentSize = commQuery.size;
		
		if (commQuery.size) {
			
			commQuery.docs.forEach(doc => {
				let { role, qiuserId, status } = doc.data();
				ticketCommissions[role] = { qiuserId, doc, status };
				if (!after.exists || !isPublished || isModel) {
					deleteCommission({ qiuserId, doc, status })
				}
			});
			
		}
	}
	
	if ( after.exists ) {
		
		for ( const role of COMMISSIONED_ROLES ) {
			
			if ( newData[role] || context[role] ) {
				
				let qiuserId = newData[role] || context[role];
				
				let commissionDoc = ticketCommissions[role] ? ticketCommissions[role].doc : undefined;

				// eslint-disable-next-line no-await-in-loop
				let newCommission = await createCommission(role, qiuserId, newData, ticketId, commissionDoc);
				
				debug.newCommission = newCommission;

				if ( newCommission ) {

					if ( ticketCommissions[role] ) {
						let { qiuserId, doc, status } = ticketCommissions[role];
						if ( !areEqualObjects(doc.data(), newCommission) ) {
							updateCommission(qiuserId, newCommission)
							debug.diffCurrent = diffObjects(doc.data(), newCommission);
							debug.diffCurrentRight = diffObjects(newCommission, doc.data());
						}
					}else{
						addCommission(qiuserId, newCommission);
					}

				}else
				if ( ticketCommissions[role] ) {
					deleteCommission(ticketCommissions[role]);
				}

			}else
			if ( ticketCommissions[role] ) {
				deleteCommission(ticketCommissions[role]);
			}
		}
	}

	debug.promises = promises.length;
	
	console.log('updateComissions', { debug })

	return Promise.all(promises)

};

// grabHereToCopyPaste
const ticketsListener = async (change, context) => {

	funcCounter++;
	console.log('ticketsListener funcCounter:', funcCounter);
	if (funcCounter > CONSTANTS.FUNCTIONS_EXECUTION_LIMIT) {
		console.error('QIPLUS WEBMASTER LIMIT MANAGER > FUNCTIONS_EXECUTION_LIMIT: '+funcCounter);
		return null;
	}

	const { before, after } = change;
	const { params } = context;

	const ticketId = params.docId;
	const oldData = (before.exists && before.data()) || {};
	const newData = (after.exists && after.data()) || {};
	const ticketRef = change.after.ref;
	
	if (oldData.systemUpdate || newData.systemUpdate) {
		console.log(`systemUpdate on > ${oldData.systemUpdate?'oldData':'newData'} > funcCounter`+funcCounter);
		return null;
	}

    const promises = [];

    promises.push(updateComissions(oldData, newData, ticketId, ticketRef, before, after));

	// LOGS
	let triggerName = '';
	const logKeys = [CONSTANTS.DATE_FIELD, CONSTANTS.MODIFIED_FIELD, ...CONSTANTS.RELATIONAL_FIELDS];
	const logData = {};
	const dataKeys = ['total', 'value'];
	
	logKeys.forEach(key=>{
		if ( change.after.exists && (key in newData) ) {
			logData[key] = newData[key];
		}else
		if ( !change.after.exists && (key in oldData) ) {
			logData[key] = oldData[key];
		}	
	})

	dataKeys.forEach(key=>{
		if ( change.after.exists && newData.data && (key in newData.data) ) {
			logData[key] = newData.data[key];
		}else
		if ( !change.after.exists && oldData.data && (key in oldData.data) ) {
			logData[key] = oldData.data[key];
		}	
	})

	const contactId = newData.contactId;
	
	if ( !before.exists && !(newData.config||{}).use_as_template && newData.status === CONSTANTS.PUBLISH_STATUS ) {
		
		if ( (newData.context||{}).id && newData.context.collection ) {

			let ticketContext = newData.context;

			let conversionLog = {
				contactId,
				user_id: contactId,
				operator_id: ticketContext.operator_id,
				id: ticketContext.id,
				collection: ticketContext.collection,
				trigger: APP_TRIGGERS.APP_TRIGGER_CONVERTED,
				date: nowToISO(),
				owner: logData.owner||'',
				accountId: logData.accountId||'',
				data: logData,
				context: {
					...ticketContext,
					collection: COLLECTIONS.TICKETS_COLLECTION_NAME,
					id: ticketId,
					operator_id: ticketContext.operator_id||0,
				}
			}

			if ( ticketContext.collection === COLLECTIONS.DEALS_COLLECTION_NAME) {
				
				let dealId = ticketContext.id;
				
				try {
					
					const dealDoc = await FirestoreRef.collection(COLLECTIONS.DEALS_COLLECTION_NAME).doc(dealId).get()
					const dealRef = dealDoc.ref;
					const deal = dealDoc.data()

					dealRef.update({ 
						stats: { 
							...deal.stats, 
							conversionTotal: parseFloat((deal.stats||{}).conversionTotal||0)+parseFloat((newData.data||{}).total||0) 
						},
						conversions: {
							...(deal.conversions||{}), 
							[ticketId]: parseFloat((newData.data||{}).total||0)
						}
					})

					CONSTANTS.RELATIONAL_FIELDS
					.filter(field=>!logData[field] && deal[field])
					.forEach(field=>{
						conversionLog.data[field] = deal[field]
					})

					const funnelId = deal[CONSTANTS.FUNNEL_ID_FIELD];
					if ( funnelId ) {
						
						/* Add funnel to context */
						conversionLog.context[CONSTANTS.FUNNEL_ID_FIELD] = funnelId
						
						await addNewLog({
							...conversionLog,
							id: funnelId,
							collection: COLLECTIONS.FUNNELS_COLLECTION_NAME,
							data: {
								...conversionLog.data,
								dealId
							},
						})
					}
				} catch (err) {
					console.log('err',err);
				}

			}

			await addNewLog(conversionLog)

		}
		
	}

	if ( change.after.exists ) {
		
		if ( newData.status !== CONSTANTS.PUBLISH_STATUS && oldData.status === CONSTANTS.PUBLISH_STATUS ) {
			addNewLog({
				contactId,
				user_id: contactId,
				operator_id: 0,
				id: ticketId,
				collection: COLLECTIONS.TICKETS_COLLECTION_NAME,
				trigger: APP_TRIGGERS.APP_TRIGGER_TICKET_CANCELED,
				date: nowToISO(),
				owner: logData.owner||'',
				accountId: logData.accountId||'',
				data: logData,
			});
		}

		// PRODUCTS LOGS
		if ( Array.isArray(newData.items) && newData.items.length ) {
			
			let productIds = [...new Set(newData.items.filter(i=>i&&i.product).map(i=>i.product))];
			let oldProducts = (Array.isArray(oldData.items) && [...new Set(oldData.items.filter(i=>i&&i.product).map(i=>i.product))]) || [];
			let addedProducts = productIds.filter(l=>!oldProducts.includes(l));
			let removedProducts = oldProducts.filter(l=>!productIds.includes(l));
		
			addedProducts.forEach(pId=>{
				addNewLog({
					contactId,
					user_id: contactId,
					operator_id: 0,
					id: pId,
					collection: COLLECTIONS.PRODUCTS_COLLECTION_NAME,
					trigger: APP_TRIGGERS.APP_TRIGGER_BOUGHT,
					date: nowToISO(),
					owner: logData.owner||'',
					accountId: logData.accountId||'',
					data: logData,
					context: {
						operator_id: 0,
						id: ticketId,
						collection: COLLECTIONS.TICKETS_COLLECTION_NAME,
					}
				});
			})
		}
        
        promises.push(generateContracts(ticketId, ticketRef, newData, oldData));
	}

	return Promise.all(promises);

}

const generateContracts = (ticketId, ticketRef, newData, oldData) => {

    const ticket = newData;
    let contactsData = {};

    // console.log('Editing Ticket:', ticketId);
    // console.log('Editing ticket:', ticket);
    // console.log('Editing > ticket: currentlyUpdating', currentlyUpdating);
    // console.log('Editing > ticket: oldData contractModel', (oldData.data||{}).contractModel );
    // console.log('Editing > ticket: newData contractModel', (newData.data||{}).contractModel );

    // Replace Shorcodes
    if ( 
        !currentlyUpdating.find(c=>c.collection===ticket.collection && c.ID===ticket.ID) 
        && 
        ticket.contactId && ticket.data && ticket.data.contractModel && !ticket.data.contractId
        && (oldData.data||{}).contractModel!==(newData.data||{}).contractModel
    ) {

        currentlyUpdating.push(ticket)
        
        const { owner, accountId, contactId, items, payment, data: { contractModel } } = ticket;

        let fetchedProducts = []
        let fetchedTaxonomies = {}

        const fetchRelated = () => {

            const promises = []
            
            let gateway = (payment && payment.gateway) || '';
            if ( gateway ) promises.push(fetchTaxonomy(COLLECTIONS.GATEWAY_TAXONOMY_NAME, COLLECTIONS.TICKETS_COLLECTION_NAME, gateway))

            let productIds = Array.isArray(items) ? [...new Set(items.filter(i=>i&&i.product).map(i=>i.product))] : []
            productIds.forEach(pId=>{
                promises.push(new Promise(resPost=>fetchPost(COLLECTIONS.PRODUCTS_COLLECTION_NAME, pId).then(resPost).catch(err=>console.log('fetchRelated > fetchPost > err',{ pId, err})||resPost(null))))
            })

            if (promises.length) {
                return Promise.all(promises).then(results=>{
                    
                    if ( gateway ) fetchedTaxonomies[COLLECTIONS.GATEWAY_TAXONOMY_NAME] = results.filter(t=>t && t.collection === COLLECTIONS.TICKETS_COLLECTION_NAME && t.ID===gateway)
                    
                    fetchedProducts = results.filter(p=>p && p.collection === COLLECTIONS.PRODUCTS_COLLECTION_NAME && productIds.indexOf(p.ID)!==-1)
                    
                    return [fetchedProducts, fetchedTaxonomies]

                })
            } else {
                return new Promise(res=>res([]))
            }				

        }

        const fetchContract = () => {

            const promises = []
            
            promises.push(fetchPost(COLLECTIONS.LEADS_COLLECTION_NAME, contactId))
            promises.push(fetchPost(COLLECTIONS.CONTRACTS_COLLECTION_NAME, contractModel))

            return Promise.all(promises)

        }
        
        const updateTicket = (addedContract) => {
            console.log('updateTicket', addedContract);
            const updatePath = "data.contractId";
            return new Promise((res,rej)=>{

                ticketRef.update({ [updatePath]: addedContract.ID })
                    .then(result=>{
                        return res(addedContract)
                    })
                    .catch(error=>{
                        console.error(error);
                        return rej(error)
                    })
                })
        }

        const releaseCache = () => {
            const i = currentlyUpdating.findIndex(c=>c.collection===COLLECTIONS.TICKETS_COLLECTION_NAME && c.ID===ticketId)
            currentlyUpdating.splice(i,1);
        }

        return fetchRelated()
        .then(results => {
            console.log('fetchRelated > results',{ results });
            return fetchContract()
        })
        .then(results => {
            console.log('fetchContract > results',{ results });
                    
            contactsData = results[0];

            const contract = results[1];
            const contractNumber = new Date().getTime();

            const newContract = {
                ...contract,
                id: "",
                ID: "",
                date: nowToISO(),
                modified: nowToISO(),
                title: `Contrato ${contractNumber}`,
                contactId,
                data: {
                    ...contract.data,
                    contractNumber,
                },
                config: {
                    ...contract.config,
                    use_as_template: 0
                },
                context: {
                    id: ticket.ID,
                    collection: COLLECTIONS.TICKETS_COLLECTION_NAME,
                    operator_id: ticket.author
                },
            }
            
            //  contact
            const { contact } = ticket;
            isLoopableObject(contact) && Object.keys(contact).forEach(k=>{
                if (contact[k]) contactsData[k] = contact[k]
            })
            
            //  content
            const { content } = contract;
            let srcCollection = COLLECTIONS.LEADS_COLLECTION_NAME;
            return replaceShortCodes(content||'', newContract, srcCollection, [ticket, contactsData, ...fetchedProducts], fetchedTaxonomies)

        })
        .then(({ content, post })=>{
            const updatedContract = {
                ...post,
                content
            }						
            return addNewPost(COLLECTIONS.CONTRACTS_COLLECTION_NAME, updatedContract)
        })
        .then(addedContract=>{
            return updateTicket(addedContract)
        })
        .then(addedContract=>{
            
            if ( contactsData.email && addedContract.settings ) {
                
                let { settings } = addedContract;
                let { from, fromName, subject } = settings;

                fromName = fromName || "";
                from = from || CONSTANTS.DEFAULT_FROM_EMAIL;
                subject = subject || DEFAULT_CONTRACT_SUBJECT;
                
                const to = contactsData.email;
                const html = addedContract.content;
                const emailVars = { contactId, srcCollection: contactsData.collection||"" };
                
                const context = {
                    contactId,
                    accountId,
                    owner,
                    collection: COLLECTIONS.CONTRACTS_COLLECTION_NAME,
                    id: addedContract.ID,
                    operator_id: ticket.author
                };
                
                const email = { ...settings, to, html, from, fromName, subject, owner, accountId, context, emailVars };

                console.log('ticketsListener > email',email);
                
                createMailing(email)
                    
            }

            return addedContract;
            
        })
        .then(result=>{
            releaseCache()		
            return result;
        })
        .catch(error=>{
            releaseCache()		
            console.log('QIPLUS: error in chained promises')
            console.error(error)
        })

    }
	
	return null;

}

module.exports = {
    ticketsListener
}