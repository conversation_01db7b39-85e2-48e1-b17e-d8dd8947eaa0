/* eslint-disable consistent-return */
const brazilFormat = (
  countryCode,
  phone,
  insertNine = true,
  removeNine = false
) => {
  // Verifica se o telefone tem o prefixo 55
  if (phone.startsWith(countryCode)) {
    const numberLength = phone.length - countryCode.length;
    phone = phone.slice(-numberLength);
  }

  // Verifica se o telefone tem o prefixo +55
  if (phone.startsWith(`+${countryCode}`)) {
    const numberLength = phone.length - `+${countryCode}`.length;
    phone = phone.slice(-numberLength);
  }

  // falta o 9º digito
  if (phone.length < 11 && insertNine) {
    const areaCode = phone.slice(0, 2);
    const phoneWithoutAreaCode = phone.slice(2, 10);
    return `${areaCode}9${phoneWithoutAreaCode}`;
  }

  // remove o 9º digito
  if (phone.length === 11 && removeNine) {
    const areaCode = phone.slice(0, 2);
    const phoneWithoutAreaCode = phone.slice(3, 11);
    return `${areaCode}${phoneWithoutAreaCode}`;
  }

  // 11 99999-9999 -> 10 : sem country code
  return phone;
};

const sanitizePhoneNumber = (countryCode, phoneNumber) => {
  if (!phoneNumber && !countryCode) return;

  const phone = phoneNumber.replace(/[^0-9]/g, ""); // Phone number with area code

  if (countryCode === "55" || countryCode === "+55") {
    return brazilFormat(countryCode, phone);
  }

  return phone;
};

const sanitizePhoneNumberWithCountryCode = (countryCode, phoneNumber) => {
  if (!phoneNumber && !countryCode) return;

  let phone = phoneNumber.replace(/[^0-9]/g, ""); // Phone number with area code

  if (countryCode === "55" || countryCode === "+55") {
    phone = brazilFormat(countryCode, phone);
  }

  const newPhone = `${countryCode}${phone}`;

  return newPhone;
};

const removeBrazilNineDigit = (phoneNumber) => {
  if (!phoneNumber) return;

  const phone = brazilFormat("55", phoneNumber, false, true);

  return phone;
};

const insertBrazilNineDigit = (phoneNumber) => {
  if (!phoneNumber) return;

  const phone = brazilFormat("55", phoneNumber, true, false);

  return phone;
};

const checkPhoneOrMobile = (lead) => {
  let contactNumber = "";

  if (lead.mobileCC) {
    lead.mobileCC === '55' ? contactNumber = insertBrazilNineDigit(lead.mobile) : contactNumber = `${lead.mobileCC}${lead.mobile}`;
  } else {
    contactNumber = `${lead.phoneCC}${lead.phone}`;
  }

  return contactNumber
}

module.exports = {
  sanitizePhoneNumber,
  sanitizePhoneNumberWithCountryCode,
  removeBrazilNineDigit,
  insertBrazilNineDigit,
  checkPhoneOrMobile
};
