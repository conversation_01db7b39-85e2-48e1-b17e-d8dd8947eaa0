/* 
API key: **************************************************
API base URL: https://api.mailgun.net/v3/mail.qiplus.com.br
*/

const moment = require("moment");
const { CONSTANTS } = require("../init");
const { isset, validateEmail, momentNow } = require("../helpers");

const EMAIL_APP_DOMAIN = 'qiplus.cloud';
const MAILING_DOMAIN = 'mail.qiplus.com.br';
const DEV_MAILING_DOMAIN = 'devmail.qiplus.com.br';
const DEFAULT_MAILING_DOMAIN = CONSTANTS.APP_ENV===CONSTANTS.DEV_ENV ? DEV_MAILING_DOMAIN : MAILING_DOMAIN;
const DEFAULT_FROM_EMAIL = `mailing@${DEFAULT_MAILING_DOMAIN}`;
const MESSAGES_ENDPOINT = `${CONSTANTS.REMOTE_URL}/mail/messages.php`;
const STORAGE_ENDPOINT = `${CONSTANTS.REMOTE_URL}/mail/store.php`;
const DEFAULT_MAILGUN_PASS = `QIPlus@Amg!`;
const eventsApiURL = 'https://api.mailgun.net/v3/events/';
const MAILGUN_API_KEY = '**************************************************';
const MAILGUN_PUB_KEY = 'pubkey-b2e168cc42dfedf46b3aa9d752ecec1e';
const MAILGUN_SCHEDULE_LIMIT_DAYS = 3;
const MAILGUN_SCHEDULE_LIMIT_TIME = MAILGUN_SCHEDULE_LIMIT_DAYS*24*60*60*1000;

const qiplusDomains = [EMAIL_APP_DOMAIN, MAILING_DOMAIN, DEV_MAILING_DOMAIN];

const mailgunjs = require("mailgun-js");
const mailingApi = mailgunjs({apiKey: MAILGUN_API_KEY, domain: DEFAULT_MAILING_DOMAIN});
const mailingApp = mailingApi.messages();
const emailApi = mailgunjs({apiKey: MAILGUN_API_KEY, domain: EMAIL_APP_DOMAIN});
const emailApp = emailApi.messages();
const publicApi = mailgunjs({apiKey: MAILGUN_PUB_KEY, domain: MAILING_DOMAIN});

const forwardMailbox = (mailbox, forwardTo, cb, domain) => {
	domain = domain||EMAIL_APP_DOMAIN;
	let api = domain === DEFAULT_MAILING_DOMAIN ? mailingApi: emailApi;
	let recipient = mailbox.indexOf('@')>=0 ? mailbox : `${mailbox}@${domain}`;
	api.post('/routes', {
		"priority": 1, 
		"description": 'Custom Forward @ QPlus', 
		"expression": `match_recipient("${recipient}")`, 
		"action": `forward("${forwardTo}")`, 
		// "action": 'stop()'
	}, (error, body)=>{
		// console.log(body);
		cb && cb({error, body})
	});
}

const getRoutes = (cb, domain) => {
	domain = domain||EMAIL_APP_DOMAIN;
	let api = domain === DEFAULT_MAILING_DOMAIN ? mailingApi: emailApi;
	api.get(`/routes`, (error, body)=>{
		// console.log(body);
		cb && cb({error, body})
	});
}
const getDomainMailboxes = (cb, domain) => {
	domain = domain||EMAIL_APP_DOMAIN;
	let api = domain === DEFAULT_MAILING_DOMAIN ? mailingApi: emailApi;
	api.get(`/${domain}/mailboxes`, (error, body)=>{
		// console.log(body);
		cb && cb({error, body})
	});
}
const createDomainMailbox = (mailbox, pass, cb, domain, forwardTo) => {
	domain = domain||EMAIL_APP_DOMAIN;
	if (mailbox.split('@').length > 1) {
		domain = mailbox.split('@')[1];
		mailbox = mailbox.split('@')[0];
	}
	if (!qiplusDomains.includes(domain)) {
		cb && cb({ error: 'Invalid domain', body: false });
		return false;
	}
	let api = domain === DEFAULT_MAILING_DOMAIN ? mailingApi: emailApi;
	let recipient = `${mailbox}@${domain}`;
	api.post(`/${domain}/mailboxes`, {"mailbox": recipient, "password": pass||DEFAULT_MAILGUN_PASS}, (error, body)=>{
		// console.log(body);
		if (error) {
			cb && cb({error, body})
			return;
		}
		api.post('/routes', {
			"priority": 0, 
			"description": 'Store QPlus Messages', 
			"expression": `match_recipient("${recipient}")`, 
			"action": `store(notify="${STORAGE_ENDPOINT}")`, 
			// "action": 'stop()'
		}, (error, body)=>{
			// console.log(body);
			if (forwardTo && validateEmail(forwardTo)) {
				forwardMailbox(recipient, forwardTo, cb);			
			}else
			if ( cb ) {
				return cb({ error, body });
			}
			return null;
		});
	});
	return null;
}
const updateMailboxPass = (mailbox, pass, cb, domain) => {
	domain = domain||EMAIL_APP_DOMAIN;
	let api = domain === DEFAULT_MAILING_DOMAIN ? mailingApi: emailApi;
	api.put(`/${domain}/mailboxes/${mailbox}`, {"password": pass}, (error, body)=>{
		// console.log(body);
		cb && cb({error, body})
	});
}
const deleteDomainMailbox = (mailbox, cb, domain) => {
	domain = domain||EMAIL_APP_DOMAIN;
	if (mailbox.split('@').length > 1) {
		domain = mailbox.split('@')[1];
		mailbox = mailbox.split('@')[0];
	}
	if (!qiplusDomains.includes(domain)) {
		cb && cb({ error: 'Invalid domain', body: false });
		return false;
	}
	let api = domain === DEFAULT_MAILING_DOMAIN ? mailingApi: emailApi;
	let recipient = `${mailbox}@${domain}`;
	api.delete(`/${domain}/mailboxes/${mailbox}`, {"mailbox": recipient}, (error, body)=>{
		// console.log(body);
		cb && cb({error, body})
	});
	return null;
}

const validateMailbox = (address, cb) => {
	publicApi.validate(address, (error, body) => {
		cb({error, body})
	})
}

let emails = {};
let messageIds = [];
let getEmailLogs = (currPage, logParams, cb) => {
	mailingApi.get(`/events${currPage.replace(eventsApiURL,'/')}`, logParams||null,  (error, body) => {
		console.log('error',error);
		console.log('items',(body.items||[]).length);	
		if (!error && (body.items||[]).length) {
			let { paging: { next }} = body;
			return cb && cb(body.items,next)
		}
		return null
	});
}

const insertEmailLogs = async (items, next, logParams, emailsRef, logsRef)=>{

	let events = [];
	let logs = [];
	let emailPromises = [];
	let logsQueries = [];

	let eventsCounter = {}

	items.forEach(data=>{
		// console.log('\ndata',data);

		let messageId = isset(data,["message","headers","message-id"]) ? data["message"]["headers"]["message-id"] : "";
		let trackId = `<${messageId}>`;
		let milis = data.timestamp*1000;
	
		if (!messageIds.includes(messageId)) {
			emailPromises.push(emailsRef.where('trackId','==',trackId).get())
			messageIds.push(messageId)
		}

		// console.log('------------- ');
		// console.log('recipient',data.recipient);
		// console.log('------------- ');
		// console.log('event:',data.event);
		// console.log('format',moment(milis).format(CONSTANTS.MOMENT_ISO));
		// console.log('------------- ');

		!eventsCounter[data.event] && (eventsCounter[data.event]=0)
		eventsCounter[data.event]++;
		
		data['messageId'] = messageId;
		data['trackId'] = trackId;

		events.push(data);
		
	})

	console.log('\n------------- ');
	console.log('eventsCounter',eventsCounter);
	console.log('------------- ');

	let emailSnapshots = await Promise.all(emailPromises);
	
	console.log('emailSnapshots',emailSnapshots.length);
		
	emailSnapshots.forEach(snapshot=>{
		snapshot.forEach(doc=>{
			let emailData = doc.data();
			let mailId = doc.id;
			let { trackId } = emailData;					
			emailData['mailId'] = mailId;
			emails[trackId] = emailData;
		})
	})
	
	// console.log('emails',emails);
	events.forEach((data,count)=>{

		let recipient = data.recipient || "";
		let trigger = data.event || "";
		let messageId = isset(data,["message","headers","message-id"]) ? data["message"]["headers"]["message-id"] : "";
		let trackId = `<${messageId}>`;
		
		if ( !emails[trackId] ) {
			console.log('emails[trackId]',Boolean(emails[trackId]));
			return
		}
		
		// console.log('\ndata',data);

		let emailData = emails[trackId];
		let { mailId } = emailData;

		data['mailId'] = mailId;

		if (trigger && isset(emailData, ['emailVars',recipient])) {

			let attemptNo = 1;
	
			if (trigger === "failed" && isset(data, ["delivery-status","attempt-no"]) && data.severity === "temporary") {
				attemptNo = Number(data["delivery-status"]["attempt-no"]);
			}
	
			attemptNo > 1 && console.log('attemptNo',attemptNo);

			if ( attemptNo === 1 ) {
	
				let vars = emailData['emailVars'][recipient];
				let context = emailData['context'];
				let collection = context['collection'];			
				let contextId = context['id'];
				let { contactId } = vars;
	
				let log = {
					user_id: contactId||'',
					contactId: contactId||'',
					operator_id: 0,
					id: contextId,
					collection: collection,
					trigger: trigger,
					date: moment(data.timestamp*1000).format(CONSTANTS.MOMENT_ISO),
					owner: emailData[CONSTANTS.OWNER_FIELD]||CONSTANTS.ORPHANS_OWNER,
					accountId: emailData[CONSTANTS.ACCOUNT_FIELD]||CONSTANTS.ORPHANS_ACCOUNT,
					data,
				};
	
				logsQueries.push({
					log,
					collection,
					contextId,
					query: logsRef.doc(collection).collection(contextId)
						.where("contactId","==",contactId)
						.where("data.trackId","==",trackId)
						.where("trigger","==",trigger)
						.get()
				})
				
			}
			
		}
	
	})

	console.log('logsQueries',logsQueries.length);

	let logsResults = await Promise.all(logsQueries.map(q=>q.query));
	
	let hasLogs = logsResults.filter(snapshot=>Boolean(snapshot.size)).length
	let noLogs = logsResults.filter(snapshot=>!snapshot.size).length

	console.log('logsResults',logsResults.length);
	console.log('hasLogs:',hasLogs);
	console.log('noLogs:',noLogs);

	logsResults.forEach((snapshot, c)=>{
		let hasLog = Boolean(snapshot.size);

		if (!hasLog) {
			let query = logsQueries[c]
			let { collection, contextId, log } = query;
			logs.push(logsRef.doc(collection).collection(contextId).add(log));
			// console.log('-----------');
			// console.log('newLog',log);
			// console.log('-----------');							
		}
	})

	console.log('logs',logs.length);

	let logsInserts = await Promise.all(logs)
	
	console.log('logsInserts',logsInserts.length);
	// console.log(logsInserts);
	console.log('next:',next);
	
	return getEmailLogs(next, logParams, (items,next)=>{
		return insertEmailLogs(items, next, logParams, emailsRef, logsRef)
	})

}

module.exports = {
    forwardMailbox,
    getRoutes,
    getDomainMailboxes,
    createDomainMailbox,
    updateMailboxPass,
    deleteDomainMailbox,
    validateMailbox,
    getEmailLogs,
    insertEmailLogs,
    emailApi,
    emailApp,
    mailingApp,
    mailingApi,
    qiplusDomains,
    MAILGUN_API_KEY,
    MAILGUN_PUB_KEY,
    EMAIL_APP_DOMAIN,
    MESSAGES_ENDPOINT,
    STORAGE_ENDPOINT,
    MAILING_DOMAIN,
    DEV_MAILING_DOMAIN,
    DEFAULT_MAILING_DOMAIN,
    MAILGUN_SCHEDULE_LIMIT_DAYS,
    MAILGUN_SCHEDULE_LIMIT_TIME,
    DEFAULT_MAILGUN_PASS,
    DEFAULT_FROM_EMAIL
}