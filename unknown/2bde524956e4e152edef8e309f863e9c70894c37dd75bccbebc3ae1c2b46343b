const moment = require('moment');
const { MOMENT_ISO } = require('../../constants');

const { jsonClone } = require('../../helpers');

const deadlineModel = require('./deadline');
const contextModel = require("./context");

const checklist = {
    title: "",
    content: "",
    thumbnail: "",

    date: moment().format(MOMENT_ISO),
    modified: moment().format(MOMENT_ISO),
    
    config: {
        notification: false,
        deadline: true
    },
    
    data: {
    },

    context: jsonClone(contextModel),
    
    tasks: [],
    deadline: jsonClone(deadlineModel),
    notifications: [],

    contactId: "",
    qiuser: "",
    team: "",
    author: "",
    owner: "",
    stores: [],
    
    status: "publish",
    url: "",

}

 module.exports = checklist;