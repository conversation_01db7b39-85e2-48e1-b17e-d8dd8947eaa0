const { CONSTANTS, COLLECTIONS, FirestoreRef } = require("../init");
const { helpers, moment, FieldValue } = require("../init");
const { validateEmailAddress } = require("../mailing");
const {
  getOthersLeads,
  fetchContactIdsFromTriggers,
} = require("../automations");

const { sTrim, createQRCode, sanitizeFieldTypes, isLoopableObject } = helpers;

const { APP_TRIGGERS } = CONSTANTS;

// grabHereToCopyPaste
const sanitizeLeadData = async (sanitizedData, change, context) => {
  const { before, after } = change;
  const { params } = context;

  const leadId = params.docId;
  const contactId = leadId;

  // Exit when the data is deleted.
  if (!change.after.exists) {
    return [{}];
  }

  const oldData = (before.exists && before.data()) || {};
  const newData = sanitizedData;
  const docRef = after.exists && change.after.ref;

  const updatedData = {};
  let { collection } = sanitizedData;

  let promises = [];
  let deleteKeys = [];

  if (newData && change.after.exists) {
    // console.log('Editing lead:', leadId);
    // console.log('Editing newData:', newData);

    // // Get the first and last names
    const firstName = newData.firstName || "";
    const lastName = newData.lastName || "";
    const displayName = `${firstName}${(lastName && " " + lastName) || ""}`;

    // Update Display Name
    if (!newData.displayName) {
      updatedData.displayName = displayName;
    }

    // Update First Name
    if (!newData.firstName) {
      updatedData.firstName = updatedData.displayName
        ? updatedData.displayName
        : (updatedData.email && updatedData.email.split("@")[0]) || "";
    }

    if (newData.email !== oldData.email || !("mailbox_verified" in newData)) {
      if (newData.email) {
        try {
          let emailValidation = await validateEmailAddress(newData.email);
          if (emailValidation && emailValidation.address) {
            updatedData.mailbox = emailValidation;
            updatedData.is_valid = Boolean(emailValidation.is_valid);
            updatedData.mailbox_verified = ["true", true].includes(
              emailValidation.mailbox_verification
            );
          }
        } catch (error) {
          console.error(error);
          updatedData.mailbox_verified = null;
        }
      } else {
        updatedData.mailbox_verified = null;
        updatedData.is_valid = false;
      }
    }

    // Update Birthday
    if (
      newData.birthday !== oldData.birthday ||
      !("birthday_verified" in newData)
    ) {
      let bday = sTrim(newData.birthday).replace(/ /g, "");
      if (bday.split("/").length === 3) {
        let birthday = bday
          .split("/")
          .reverse()
          .map((v, i) => {
            let val = v;
            switch (i) {
              case 0:
                if (val.toString().length < 4)
                  val =
                    Number("20" + val) - 1 >= new Date().getFullYear()
                      ? "19" + val
                      : "20" + val;
                break;
              default:
                while (val.toString().length < 2) val = "0" + val;
                break;
            }
            return val;
          })
          .join("-");
        updatedData.birthday = birthday;
      }

      let momentBday = moment(updatedData.birthday || bday);
      let isValidDate = momentBday.isValid();

      updatedData.birthday_verified = isValidDate;

      if (isValidDate) {
        updatedData.birth = {
          year: momentBday.format("YYYY"),
          month: momentBday.format("MM"),
          day: momentBday.format("DD"),
        };
      }
    }

    // Update Address
    CONSTANTS.LOCATION_FIELDS_MAP.forEach((k) => {
      if (
        newData.address &&
        k in newData.address &&
        newData.address[k] !== newData[k]
      ) {
        updatedData[k] = newData.address[k];
      }
    });

    CONSTANTS.ADDRESS_FIELDS_MAP.concat(
      CONSTANTS.ADDRESS_SHORTCODES_FIELDS_MAP
    ).forEach((k) => {
      if (k in newData && CONSTANTS.LOCATION_FIELDS_MAP.indexOf(k) === -1) {
        let dbKey = k.replace(`${CONSTANTS.ADDRESS_FIELDS_GROUP}:`, "");

        if (!updatedData.address) updatedData.address = {};
        updatedData.address[dbKey] = newData[k];

        delete newData[k];
        deleteKeys.push(k);
      }
    });

    // Custom Fields
    Object.keys(newData).forEach((field) => {
      if (field.indexOf(`${CONSTANTS.CUSTOM_FIELDS_GROUP}:`) === 0) {
        // let
        updatedData[CONSTANTS.CUSTOM_FIELDS_GROUP] = {
          ...(newData[CONSTANTS.CUSTOM_FIELDS_GROUP] || {}),
          ...(updatedData[CONSTANTS.CUSTOM_FIELDS_GROUP] || {}),
          [field]: isLoopableObject(newData[field])
            ? JSON.parse(JSON.stringify(newData[field]))
            : newData[field],
        };
        delete newData[field];
        deleteKeys.push(field);
      }
    });

    // Set QR Code
    if (!newData.qrcode) {
      updatedData.qrcode = createQRCode(newData);
    }
  }

  // merge changes
  const mergedData = { ...newData, ...updatedData };

  // sanitize Field Types
  sanitizedData = sanitizeFieldTypes(mergedData);

  let hasUpdates = false;
  const updateKeys = [];

  Object.keys(sanitizedData).forEach((k) => {
    const updatedValue = sanitizedData[k];
    const newValue = newData[k];
    if (helpers.isCyclic(newValue) || helpers.isCyclic(updatedValue)) return;
    if (
      typeof newValue !== typeof updatedValue ||
      (k in updatedData &&
        JSON.stringify(newValue) !== JSON.stringify(updatedValue))
    ) {
      hasUpdates = true;
      updateKeys.push(k);
    }
  });

  hasUpdates = hasUpdates || Boolean(deleteKeys.length);

  let updateObj = {};
  if (hasUpdates && change.after.exists) {
    updateObj = { systemUpdate: true };
    updateKeys.forEach((k) => {
      updateObj[k] = sanitizedData[k];
    });
    deleteKeys.forEach((k) => {
      updateObj[k] = FieldValue.delete();
    });
    console.log("sanitizeLeadData > hasUpdates", {
      updateKeys,
      deleteKeys,
      updateObj,
    });
  }

  // console.log('sanitizeLeadData > sanitizedData', sanitizedData);
  // console.log('sanitizeLeadData > mergedData', mergedData);

  return [
    updateObj,
    async () => {
      if (promises.length) {
        await Promise.all(promises).catch(console.error);
      }
      return null;
    },
  ];
};

const getLeadsIdsFromSegmentation = async (segmentation) => {
  const accountId = segmentation.accountId;
  const entrances = segmentation.triggers.map((trigger) => {
    const entrances = [];
    trigger.entrances.map((entrance) => {
      if (
        entrance.collection === COLLECTIONS.SHOTX_COLLECTION_NAME ||
        entrance.collection === COLLECTIONS.OTHERS_COLLECTION_NAME ||
        entrance.collection === COLLECTIONS.LOCALES_COLLECTION_NAME
      ) {
        entrances.push(entrance);
      }
      return entrance;
    });
    return entrances;
  });

  let leads = [];

  return getOthersLeads(entrances, accountId)
    .then((leadsIds) => {
      leads.push(leadsIds);
      return fetchContactIdsFromTriggers(segmentation);
    })
    .then((contactIds) => {
      contactIds = [...leads[0], ...contactIds];
      return { data: { contactIds } };
    })
    .catch((error) => {
      console.log("EERROR", error);
    });
};

const getLeadById = async (leadId) => {
  try {
    if (!leadId) {
      console.error("SHOTXCRON > getLeadById > ERROR: leadId is required");
      return null;
    }

    const leadDoc = await FirestoreRef.collection(
      COLLECTIONS.LEADS_COLLECTION_NAME
    )
      .doc(leadId)
      .get();

    if (!leadDoc.exists) {
      console.log(`SHOTXCRON > Lead with ID ${leadId} not found`);
      return null;
    }

    return leadDoc.data();
  } catch (error) {
    console.error(`SHOTXCRON > Error fetching lead with ID ${leadId}:`, error);
    throw error;
  }
};

module.exports = {
  sanitizeLeadData,
  getLeadsIdsFromSegmentation,
  getLeadById,
};
