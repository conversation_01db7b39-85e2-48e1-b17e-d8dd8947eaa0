const admin = require('firebase-admin');
const functions = require('firebase-functions');
const upload = require('react-native-firebase-upload');
const cors = require('cors')({ origin: true });
const moment = require('moment');
const info = functions.config().info;
const debug = functions.logger.debug;
const { FieldValue, FieldPath } = admin.firestore;

const models = require('./models');
const helpers = require('./helpers');
const CONSTANTS = require('./constants');
const I18N = require('./constants/lang')
const ROLES = require('./constants/roles')
const COLLECTIONS = require('./constants/collections');
const ERROR_TYPES = require('./constants/errors')

const { KEYWORDS_SUBCOLLECTIONS_MAP, KEYWORDS_SUBCOLLECTION_NAME, KEYWORDS_FIELDS_MAP } = COLLECTIONS;

const FIRESTORE_EVENTS = {
    WRITE: `google.firestore.document.write`,
    CREATE: `google.firestore.document.create`,
    UPDATE: `google.firestore.document.update`,
    DELETE: `google.firestore.document.delete`,    
}

const adminSMTPConfig = { 
    user: '<EMAIL>', 
    pass: 'Fck123@Amg!', 
    name: 'Admin || QIPlus',
    service: '', 
    host: 'smtp.zoho.com',
    port: 465, 
    encryption: 'ssl' 
}

const {
    DEFAULT_LANG,
	DEFAULT_LOCALE,
	MOMENT_ISO,
} = CONSTANTS;

const { CRONJOB_TYPES } = CONSTANTS;

const {
    strings,
    i18nLabels
} = I18N;

const { momentNow } = helpers;

let userLang = DEFAULT_LANG;
let locale = DEFAULT_LOCALE;
const langMessages = strings[userLang];

moment.locale(locale);

// ----------------------------------------------------------------------------------------------------
// initialize
// ----------------------------------------------------------------------------------------------------
admin.initializeApp();

const FirestoreRef = admin.firestore();
const adminAuth = admin.auth();

module.exports = {
    CRONJOB_TYPES,
    COLLECTIONS,
    KEYWORDS_SUBCOLLECTION_NAME,
    KEYWORDS_SUBCOLLECTIONS_MAP,
    KEYWORDS_FIELDS_MAP,
    ERROR_TYPES,
    CONSTANTS,
    FIRESTORE_EVENTS,
    ROLES,
    I18N,
    i18nLabels,
    adminSMTPConfig,
    langMessages,
    admin,
    FieldValue,
    FieldPath,
    moment,
    momentNow,
    models,
    helpers,
    functions,
    upload,
    cors,
    debug,
    info,
    FirestoreRef,
    adminAuth,
}